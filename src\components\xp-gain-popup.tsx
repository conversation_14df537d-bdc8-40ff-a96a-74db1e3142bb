'use client'

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Star, TrendingUp, Award, Sparkles } from 'lucide-react';
import { cn } from '@/lib/utils';
import { createClient } from '../../supabase/client';
import { 
  calculateMatchXP, 
  awardExperience, 
  getLevelInfo, 
  formatXP, 
  getLevelTitle 
} from '@/utils/level-system';

interface XPGainPopupProps {
  matchId: string;
  finalRank: number;
  totalPlayers: number;
  score: number;
  difficulty: string;
  eliminationRound?: number;
  onClose: () => void;
  open: boolean;
}

export default function XPGainPopup({
  matchId,
  finalRank,
  totalPlayers,
  score,
  difficulty,
  eliminationRound,
  onClose,
  open
}: XPGainPopupProps) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [xpGain, setXpGain] = useState<number>(0);
  const [xpBreakdown, setXpBreakdown] = useState<string[]>([]);
  const [oldLevel, setOldLevel] = useState<number>(1);
  const [newLevel, setNewLevel] = useState<number>(1);
  const [leveledUp, setLeveledUp] = useState(false);
  const [showLevelUp, setShowLevelUp] = useState(false);
  const [currentStep, setCurrentStep] = useState<'loading' | 'xp' | 'levelup'>('loading');

  useEffect(() => {
    if (open) {
      loadXPData();
    }
  }, [open]);

  const loadXPData = async () => {
    setLoading(true);
    setError(null);
    setCurrentStep('loading');
    
    try {
      const supabase = createClient();
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        setError('User not authenticated');
        return;
      }

      // Get current player level
      const { data: playerData } = await supabase
        .from('players')
        .select('level_experience')
        .eq('id', user.id)
        .single();

      const currentXP = playerData?.level_experience || 0;
      const currentLevelInfo = getLevelInfo(currentXP);
      setOldLevel(currentLevelInfo.currentLevel);

      // Calculate XP gain
      const xpGainData = calculateMatchXP(
        finalRank,
        totalPlayers,
        score,
        difficulty,
        eliminationRound
      );

      setXpGain(xpGainData.totalXP);
      setXpBreakdown(xpGainData.breakdown);

      // Award XP to player
      const result = await awardExperience(user.id, xpGainData.totalXP);
      
      if (result.success && result.newLevel && result.leveledUp) {
        setNewLevel(result.newLevel);
        setLeveledUp(true);
      } else {
        setNewLevel(currentLevelInfo.currentLevel);
        setLeveledUp(false);
      }

      setCurrentStep('xp');
    } catch (error) {
      console.error('Error loading XP data:', error);
      setError('Failed to load XP data');
    } finally {
      setLoading(false);
    }
  };

  const handleContinue = () => {
    if (leveledUp) {
      setCurrentStep('levelup');
      setShowLevelUp(true);
    } else {
      onClose();
    }
  };

  const handleLevelUpContinue = () => {
    setShowLevelUp(false);
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-md">
        {currentStep === 'loading' && (
          <div className="py-8 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-600 mx-auto mb-4"></div>
            <p className="text-lg font-medium text-amber-900">Calculating XP...</p>
          </div>
        )}

        {currentStep === 'xp' && !loading && !error && (
          <>
            <DialogHeader>
              <DialogTitle className="flex items-center justify-center gap-2 text-amber-900 text-xl">
                <TrendingUp className="h-6 w-6 text-amber-600" />
                Experience Gained
              </DialogTitle>
            </DialogHeader>
            
            <div className="py-4 text-center">
              <div className="bg-amber-50 rounded-lg p-4 mb-4 border border-amber-200">
                <h3 className="text-3xl font-bold text-amber-700 mb-2">
                  +{formatXP(xpGain)}
                </h3>
                
                <div className="space-y-1 text-sm text-gray-600">
                  {xpBreakdown.map((line, index) => (
                    <p key={index}>{line}</p>
                  ))}
                </div>
              </div>
              
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-gray-600">Current Level</span>
                    <Badge className={getLevelBadgeStyle(oldLevel)}>
                      Level {oldLevel}
                    </Badge>
                  </div>
                  
                  {leveledUp ? (
                    <div className="flex items-center gap-2 text-green-600 font-medium">
                      <Sparkles className="h-4 w-4" />
                      <span>Level Up! You've reached Level {newLevel}</span>
                    </div>
                  ) : (
                    <p className="text-sm text-gray-600">
                      Keep playing to reach Level {oldLevel + 1}
                    </p>
                  )}
                </div>
              </div>
            </div>
            
            <div className="flex justify-center">
              <Button 
                onClick={handleContinue}
                className="bg-amber-600 hover:bg-amber-700"
              >
                Continue
              </Button>
            </div>
          </>
        )}

        {currentStep === 'levelup' && showLevelUp && (
          <div className="py-8 text-center">
            <div className="bg-gradient-to-r from-yellow-400 to-orange-500 -mx-6 -mt-6 pt-8 pb-6 px-6 rounded-t-lg">
              <div className="flex items-center justify-center gap-3 mb-4">
                <Award className="h-12 w-12 text-white animate-bounce" />
                <div>
                  <h2 className="text-3xl font-bold text-white">LEVEL UP!</h2>
                  <p className="text-xl text-white">Level {newLevel}</p>
                </div>
                <Award className="h-12 w-12 text-white animate-bounce" />
              </div>
              
              <p className="text-lg text-white mb-2">You are now a</p>
              <p className="text-2xl font-bold text-white mb-4">{getLevelTitle(newLevel)}</p>
            </div>
            
            <div className="py-4">
              <p className="text-lg font-medium text-gray-700 mb-4">
                New rewards unlocked!
              </p>
              
              {newLevel >= 5 && newLevel < 15 && (
                <div className="space-y-2">
                  <Badge className="bg-blue-100 text-blue-800">Rare Content</Badge>
                  <p className="text-sm text-gray-600">
                    Check the profile settings to see your new avatars and backgrounds!
                  </p>
                </div>
              )}
              
              {newLevel >= 15 && newLevel < 50 && (
                <div className="space-y-2">
                  <Badge className="bg-purple-100 text-purple-800">Epic Content</Badge>
                  <p className="text-sm text-gray-600">
                    Check the profile settings to see your new epic avatars, backgrounds and borders!
                  </p>
                </div>
              )}
              
              {newLevel >= 50 && (
                <div className="space-y-2">
                  <Badge className="bg-yellow-100 text-yellow-800">Legendary Content</Badge>
                  <p className="text-sm text-gray-600">
                    Check the profile settings to see your new legendary content!
                  </p>
                </div>
              )}
            </div>
            
            <Button 
              onClick={handleLevelUpContinue}
              className="bg-amber-600 hover:bg-amber-700"
            >
              Awesome!
            </Button>
          </div>
        )}

        {error && (
          <div className="py-8 text-center">
            <p className="text-red-600 mb-4">{error}</p>
            <Button 
              onClick={onClose}
              variant="outline"
            >
              Close
            </Button>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}

function getLevelBadgeStyle(level: number) {
  if (level >= 50) return "bg-gradient-to-r from-yellow-400 to-orange-500 text-white shadow-lg";
  if (level >= 25) return "bg-gradient-to-r from-purple-400 to-pink-500 text-white shadow-md";
  if (level >= 15) return "bg-gradient-to-r from-blue-400 to-cyan-500 text-white shadow-md";
  if (level >= 5) return "bg-gradient-to-r from-green-400 to-emerald-500 text-white shadow-sm";
  return "bg-gradient-to-r from-gray-400 to-gray-500 text-white";
}
