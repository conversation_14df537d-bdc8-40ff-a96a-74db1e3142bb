import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import Script from "next/script";
import { TempoInit } from "@/components/tempo-init";
import { ThemeProvider } from "@/components/theme-provider";
import { Toaster } from "sonner";
import { AudioProvider } from "@/contexts/audio-context";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Word Nook",
  description: "A modern full-stack starter template powered by Next.js",
  icons: {
    icon: "/logo.png",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <Script src="https://api.tempolabs.ai/proxy-asset?url=https://storage.googleapis.com/tempo-public-assets/error-handling.js" />
      <body className={inter.className}>
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem
          disableTransitionOnChange
        >
          <AudioProvider>
            {children}
            <Toaster position="top-right" />
          </AudioProvider>
        </ThemeProvider>
        <TempoInit />
      </body>
    </html>
  );
}
