import DashboardNavbar from "@/components/dashboard-navbar";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  Trophy,
} from "lucide-react";
import { createClient } from "../../../supabase/server";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { cn } from "@/lib/utils";

export default async function HallOfFamePage() {
  const supabase = await createClient();

  // Mock data for Hall of Fame
  const hallOfFameData = {
    Easy: [
      { username: "S<PERSON><PERSON><PERSON>", score: 980, date: "2023-05-15" },
      { username: "WordWizard", score: 920, date: "2023-05-16" },
      { username: "LexiconMaster", score: 890, date: "2023-05-14" },
      { username: "AlphabetAce", score: 850, date: "2023-05-17" },
      { username: "<PERSON><PERSON><PERSON>", score: 820, date: "2023-05-13" },
      { username: "<PERSON><PERSON><PERSON><PERSON>ict<PERSON>", score: 790, date: "2023-05-18" },
      { username: "Spell<PERSON>hecker", score: 760, date: "2023-05-12" },
      { username: "WordSmith", score: 730, date: "2023-05-19" },
      { username: "LetterLegend", score: 700, date: "2023-05-11" },
      { username: "DictionaryDiva", score: 670, date: "2023-05-20" },
    ],
    Medium: [
      { username: "SyntaxSage", score: 880, date: "2023-05-15" },
      { username: "PhrasePhenom", score: 840, date: "2023-05-16" },
      { username: "WordWeaver", score: 800, date: "2023-05-14" },
      { username: "SpellSavant", score: 760, date: "2023-05-17" },
      { username: "LexicalLion", score: 720, date: "2023-05-13" },
      { username: "GlossaryGiant", score: 680, date: "2023-05-18" },
      { username: "ThesaurusThunder", score: 640, date: "2023-05-12" },
      { username: "VerbVirtuoso", score: 600, date: "2023-05-19" },
      { username: "NounNinja", score: 560, date: "2023-05-11" },
      { username: "AdjectiveAce", score: 520, date: "2023-05-20" },
    ],
    Hard: [
      { username: "OrthographyOracle", score: 780, date: "2023-05-15" },
      { username: "LinguistLegend", score: 750, date: "2023-05-16" },
      { username: "EtymologyExpert", score: 720, date: "2023-05-14" },
      { username: "SyllableSage", score: 690, date: "2023-05-17" },
      { username: "PhonicsPhenom", score: 660, date: "2023-05-13" },
      { username: "MorphologyMaster", score: 630, date: "2023-05-18" },
      { username: "SemanticsScholar", score: 600, date: "2023-05-12" },
      { username: "SyntaxStar", score: 570, date: "2023-05-19" },
      { username: "GrammarGiant", score: 540, date: "2023-05-11" },
      { username: "LexiconLegend", score: 510, date: "2023-05-20" },
    ],
    Extreme: [
      { username: "WordsWorth", score: 680, date: "2023-05-15" },
      { username: "SpellboundSage", score: 650, date: "2023-05-16" },
      { username: "LexicalLuminary", score: 620, date: "2023-05-14" },
      { username: "VerboseVirtuoso", score: 590, date: "2023-05-17" },
      { username: "PhilologyPhenom", score: 560, date: "2023-05-13" },
      { username: "GlossaryGuru", score: 530, date: "2023-05-18" },
      { username: "DictionaryDuke", score: 500, date: "2023-05-12" },
      { username: "ThesaurusThane", score: 470, date: "2023-05-19" },
      { username: "WordplayWizard", score: 440, date: "2023-05-11" },
      { username: "SpellingSupreme", score: 410, date: "2023-05-20" },
    ],
  };

  // Mock data for room counts - needed for difficultyRooms.map in Hall of Fame section
  const difficultyRooms = [
    {
      name: "Easy",
      users: 12,
      icon: BookOpen,
      color: "text-green-500",
      bgColor: "bg-green-100",
    },
    {
      name: "Medium",
      users: 8,
      icon: Zap,
      color: "text-blue-500",
      bgColor: "bg-blue-100",
    },
    {
      name: "Hard",
      users: 5,
      icon: Flame,
      color: "text-orange-500",
      bgColor: "bg-orange-100",
    },
    {
      name: "Extreme",
      users: 2,
      icon: Crown,
      color: "text-purple-500",
      bgColor: "bg-purple-100",
    },
  ];

  return (
    <>
      <DashboardNavbar />
      <main className="w-full bg-amber-50/30 min-h-screen">
        <div className="container mx-auto px-4 py-8 flex flex-col gap-8">
          {/* Header Section */}
          <header className="flex flex-col gap-4">
            <h1 className="text-3xl font-bold text-amber-900">
              Hall of Fame
            </h1>
            <p className="text-sm text-muted-foreground">
              Top 10 highest scores for each difficulty
            </p>
          </header>

          {/* Hall of Fame Section */}
          <section className="bg-white rounded-xl p-6 border shadow-sm">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {difficultyRooms.map((room) => (
                <div
                  key={`hall-of-fame-${room.name}`}
                  className="border rounded-lg overflow-hidden"
                >
                  <div
                    className={cn(
                      "p-4 flex items-center justify-between",
                      room.bgColor,
                    )}
                  >
                    <div className="flex items-center gap-2">
                      <room.icon className={cn("h-5 w-5", room.color)} />
                      <h3 className="font-semibold text-lg">{room.name}</h3>
                    </div>
                    <span className="text-xs font-medium px-2 py-1 bg-white/80 rounded-full">
                      Top 10 Players
                    </span>
                  </div>

                  <div className="divide-y">
                    {hallOfFameData[room.name as keyof typeof hallOfFameData]?.map((entry, index) => (
                      <div
                        key={`${room.name}-${index}`}
                        className="flex items-center justify-between p-3 hover:bg-amber-50/50"
                      >
                        <div className="flex items-center gap-3">
                          <div
                            className={cn(
                              "w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold",
                              index === 0
                                ? "bg-amber-500 text-white"
                                : index === 1
                                  ? "bg-gray-400 text-white"
                                  : index === 2
                                    ? "bg-amber-700 text-white"
                                    : "bg-amber-100 text-amber-800",
                            )}
                          >
                            {index + 1}
                          </div>
                          <Avatar className="h-6 w-6">
                            <AvatarFallback className="text-xs">
                              {entry.username.substring(0, 2)}
                            </AvatarFallback>
                          </Avatar>
                          <span className="font-medium">{entry.username}</span>
                        </div>
                        <div className="flex items-center gap-4">
                          <span className="text-sm text-gray-500">
                            {entry.date}
                          </span>
                          <span className="font-bold text-amber-800">
                            {entry.score}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </section>
        </div>
      </main>
    </>
  );
}
