import DashboardNavbar from "@/components/dashboard-navbar";
import {
  <PERSON><PERSON><PERSON>,
  Zap,
  Flame,
  Crown,
  Trophy,
  TrendingUp,
  Users,
  Target,
  Clock,
  Medal,
  Star,
  Award,
  BarChart3
} from "lucide-react";
import { createClient } from "../../../supabase/server";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { getLeaderboardByDifficulty, getLeaderboardStats, getGlobalLeaderboard } from "@/utils/leaderboard-utils";

export default async function LeaderboardPage() {
  const supabase = await createClient();

  // Get real leaderboard data for each difficulty
  const [easyLeaderboard, mediumLeaderboard, hardLeaderboard, extremeLeaderboard] = await Promise.all([
    getLeaderboardByDifficulty('easy', 10),
    getLeaderboardByDifficulty('medium', 10),
    getLeaderboardByDifficulty('hard', 10),
    getLeaderboardByDifficulty('extreme', 10)
  ]);

  // Get stats for each difficulty
  const [easyStats, mediumStats, hardStats, extremeStats] = await Promise.all([
    getLeaderboardStats('easy'),
    getLeaderboardStats('medium'),
    getLeaderboardStats('hard'),
    getLeaderboardStats('extreme')
  ]);

  // Get global leaderboard
  const globalLeaderboard = await getGlobalLeaderboard(15);

  const leaderboardData = {
    Easy: easyLeaderboard,
    Medium: mediumLeaderboard,
    Hard: hardLeaderboard,
    Extreme: extremeLeaderboard,
  };

  const statsData = {
    Easy: easyStats,
    Medium: mediumStats,
    Hard: hardStats,
    Extreme: extremeStats,
  };

  const difficultyRooms = [
    {
      name: "Easy",
      icon: BookOpen,
      color: "text-green-500",
      bgColor: "bg-green-100",
      borderColor: "border-green-200",
      gradientFrom: "from-green-50",
      gradientTo: "to-green-100",
    },
    {
      name: "Medium",
      icon: Zap,
      color: "text-blue-500",
      bgColor: "bg-blue-100",
      borderColor: "border-blue-200",
      gradientFrom: "from-blue-50",
      gradientTo: "to-blue-100",
    },
    {
      name: "Hard",
      icon: Flame,
      color: "text-orange-500",
      bgColor: "bg-orange-100",
      borderColor: "border-orange-200",
      gradientFrom: "from-orange-50",
      gradientTo: "to-orange-100",
    },
    {
      name: "Extreme",
      icon: Crown,
      color: "text-purple-500",
      bgColor: "bg-purple-100",
      borderColor: "border-purple-200",
      gradientFrom: "from-purple-50",
      gradientTo: "to-purple-100",
    },
  ];

  // Helper function to format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Helper function to get rank styling
  const getRankStyling = (index: number) => {
    if (index === 0) return "bg-gradient-to-r from-yellow-400 to-yellow-500 text-white shadow-lg";
    if (index === 1) return "bg-gradient-to-r from-gray-300 to-gray-400 text-white shadow-md";
    if (index === 2) return "bg-gradient-to-r from-amber-600 to-amber-700 text-white shadow-md";
    return "bg-gradient-to-r from-amber-100 to-amber-200 text-amber-800";
  };

  return (
    <>
      <DashboardNavbar />
      <main className="w-full bg-gradient-to-br from-amber-50/50 via-white to-amber-50/30 min-h-screen">
        <div className="container mx-auto px-4 py-8 flex flex-col gap-8">
          {/* Header Section */}
          <header className="text-center space-y-4">
            <div className="flex items-center justify-center gap-3">
              <Trophy className="h-8 w-8 text-amber-500" />
              <h1 className="text-4xl font-bold bg-gradient-to-r from-amber-600 to-amber-800 bg-clip-text text-transparent">
                Leaderboard
              </h1>
              <Trophy className="h-8 w-8 text-amber-500" />
            </div>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Compete with the best spellers! Rankings are based on composite scores including performance, survival rate, accuracy, and speed.
            </p>
          </header>

          {/* Global Top Performers */}
          {globalLeaderboard.length > 0 && (
            <section className="bg-gradient-to-r from-amber-100 via-white to-amber-100 rounded-2xl p-6 border-2 border-amber-200 shadow-lg">
              <div className="flex items-center gap-3 mb-6">
                <Award className="h-6 w-6 text-amber-600" />
                <h2 className="text-2xl font-bold text-amber-900">Global Champions</h2>
                <Badge variant="secondary" className="bg-amber-200 text-amber-800">
                  Top {globalLeaderboard.length} Players
                </Badge>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {globalLeaderboard.slice(0, 6).map((player, index) => (
                  <Card key={player.player_id} className={cn(
                    "relative overflow-hidden transition-all duration-300 hover:shadow-lg hover:scale-105",
                    index < 3 ? "ring-2 ring-amber-300" : ""
                  )}>
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3 mb-3">
                        <div className={cn(
                          "w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold",
                          getRankStyling(index)
                        )}>
                          {index + 1}
                        </div>
                        <Avatar className="h-8 w-8">
                          {player.avatar_url ? (
                            <AvatarImage src={player.avatar_url} alt={player.display_name} />
                          ) : null}
                          <AvatarFallback className="text-sm font-semibold">
                            {player.display_name.substring(0, 2).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <p className="font-semibold text-gray-900 truncate">{player.display_name}</p>
                          <Badge variant="outline" className={cn(
                            "text-xs capitalize",
                            player.difficulty === 'easy' && "border-green-300 text-green-700",
                            player.difficulty === 'medium' && "border-blue-300 text-blue-700",
                            player.difficulty === 'hard' && "border-orange-300 text-orange-700",
                            player.difficulty === 'extreme' && "border-purple-300 text-purple-700"
                          )}>
                            {player.difficulty}
                          </Badge>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-600">Composite Score</span>
                          <span className="font-bold text-amber-700">{player.composite_score.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-600">Best Score</span>
                          <span className="font-semibold">{player.final_score.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-600">Matches</span>
                          <span className="text-sm">{player.matches_played}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </section>
          )}

          {/* Difficulty-based Leaderboards */}
          <section className="space-y-8">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Difficulty Rankings</h2>
              <p className="text-gray-600">Top performers in each difficulty level</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {difficultyRooms.map((room) => {
                const roomData = leaderboardData[room.name as keyof typeof leaderboardData];
                const roomStats = statsData[room.name as keyof typeof statsData];

                return (
                  <Card
                    key={`leaderboard-${room.name}`}
                    className={cn(
                      "overflow-hidden border-2 shadow-lg transition-all duration-300 hover:shadow-xl",
                      room.borderColor
                    )}
                  >
                    {/* Header */}
                    <CardHeader className={cn(
                      "bg-gradient-to-r p-6",
                      room.gradientFrom,
                      room.gradientTo
                    )}>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <room.icon className={cn("h-6 w-6", room.color)} />
                          <CardTitle className="text-xl">{room.name}</CardTitle>
                        </div>
                        <Badge variant="secondary" className="bg-white/80">
                          {roomData.length} Players
                        </Badge>
                      </div>

                      {/* Stats */}
                      <div className="grid grid-cols-3 gap-4 mt-4">
                        <div className="text-center">
                          <p className="text-2xl font-bold text-gray-900">{roomStats.total_players}</p>
                          <p className="text-xs text-gray-600">Total Players</p>
                        </div>
                        <div className="text-center">
                          <p className="text-2xl font-bold text-gray-900">{roomStats.highest_score.toLocaleString()}</p>
                          <p className="text-xs text-gray-600">Highest Score</p>
                        </div>
                        <div className="text-center">
                          <p className="text-2xl font-bold text-gray-900">{roomStats.total_matches}</p>
                          <p className="text-xs text-gray-600">Total Matches</p>
                        </div>
                      </div>
                    </CardHeader>

                    {/* Leaderboard */}
                    <CardContent className="p-0">
                      {roomData.length === 0 ? (
                        <div className="p-8 text-center text-gray-500">
                          <Trophy className="h-12 w-12 mx-auto mb-3 text-gray-300" />
                          <p className="text-lg font-medium">No matches yet</p>
                          <p className="text-sm">Be the first to compete in {room.name} mode!</p>
                        </div>
                      ) : (
                        <div className="divide-y divide-gray-100">
                          {roomData.map((player, index) => (
                            <div
                              key={`${room.name}-${player.player_id}`}
                              className="flex items-center justify-between p-4 hover:bg-gray-50/50 transition-colors"
                            >
                              <div className="flex items-center gap-4">
                                <div className={cn(
                                  "w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold",
                                  getRankStyling(index)
                                )}>
                                  {index + 1}
                                </div>
                                <Avatar className="h-8 w-8">
                                  {player.avatar_url ? (
                                    <AvatarImage src={player.avatar_url} alt={player.display_name} />
                                  ) : null}
                                  <AvatarFallback className="text-sm font-semibold">
                                    {player.display_name.substring(0, 2).toUpperCase()}
                                  </AvatarFallback>
                                </Avatar>
                                <div className="flex-1 min-w-0">
                                  <p className="font-semibold text-gray-900 truncate">{player.display_name}</p>
                                  <div className="flex items-center gap-2 mt-1">
                                    <Badge variant="outline" className="text-xs">
                                      {player.matches_played} matches
                                    </Badge>
                                    <span className="text-xs text-gray-500">
                                      {formatDate(player.last_played)}
                                    </span>
                                  </div>
                                </div>
                              </div>

                              <div className="text-right">
                                <p className="font-bold text-lg text-amber-700">
                                  {player.composite_score.toLocaleString()}
                                </p>
                                <p className="text-sm text-gray-600">
                                  Best: {player.final_score.toLocaleString()}
                                </p>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </section>
        </div>
      </main>
    </>
  );
}
