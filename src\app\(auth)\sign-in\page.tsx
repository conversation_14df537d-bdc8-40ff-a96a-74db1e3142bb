import { signInAction } from "@/app/actions";
import { FormMessage, Message } from "@/components/form-message";
import Navbar from "@/components/navbar";
import { SubmitButton } from "@/components/submit-button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Link from "next/link";
import { GoogleSignInButton } from "@/components/ui/google-signin-button";
import Image from "next/image";

interface LoginProps {
  searchParams: Promise<Message>;
}

export default async function SignInPage({ searchParams }: LoginProps) {
  const message = await searchParams;

  if ("message" in message) {
    return (
      <div className="flex h-screen w-full flex-1 items-center justify-center p-4 sm:max-w-md">
        <FormMessage message={message} />
      </div>
    );
  }

  return (
    <>
      <div className="flex min-h-screen bg-[#fff8e8]">
        {/* Left panel - decorative */}
        <div className="hidden md:flex md:w-1/2 bg-gradient-to-br from-[#8B4513] to-[#A0522D] flex-col items-center justify-center p-12 text-white">
          <div className="w-full max-w-md space-y-6">
            <div className="flex justify-center mb-8">
              <Image src="/logo2.png" alt="Word Nook Logo" width={180} height={60} className="drop-shadow-md bg-[#fff8e8] rounded-lg" />
            </div>
            <h2 className="text-3xl font-bold tracking-tight text-center">Welcome back to Word Nook</h2>
            <p className="text-lg text-center opacity-90">Your journey through words continues here.</p>
            <div className="mt-8 p-6 bg-white/10 rounded-lg backdrop-blur-sm border border-white/20 shadow-xl">
              <p className="italic text-white/90">"The more that you read, the more things you will know. The more that you learn, the more places you'll go."</p>
              <p className="mt-2 text-right text-white/80">— Dr. Seuss</p>
            </div>
          </div>
        </div>

        {/* Right panel - form */}
        <div className="w-full md:w-1/2 flex items-center justify-center p-4 md:p-12">
          <div className="w-full max-w-md">
            <div className="md:hidden flex justify-center mb-8">
              <Image src="/logo2.png" alt="Word Nook Logo" width={150} height={50} />
            </div>

            <div className="bg-white rounded-xl border border-stone-200 shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl">
              <div className="p-8">
                <form className="flex flex-col space-y-6">
                  <div className="space-y-2">
                    <h1 className="text-3xl font-bold tracking-tight text-stone-800">Sign in</h1>
                    <p className="text-sm text-stone-500">
                      Don't have an account?{" "}
                      <Link
                        className="text-[#8B4513] font-medium hover:underline transition-all"
                        href="/sign-up"
                      >
                        Sign up
                      </Link>
                    </p>
                  </div>

                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="email" className="text-sm font-medium text-stone-700">
                        Email
                      </Label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        placeholder="<EMAIL>"
                        required
                        className="w-full border-stone-300 focus:border-[#8B4513] focus:ring-[#8B4513]/20 transition-all"
                      />
                    </div>

                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <Label htmlFor="password" className="text-sm font-medium text-stone-700">
                          Password
                        </Label>
                        <Link
                          className="text-xs text-stone-500 hover:text-[#8B4513] hover:underline transition-all"
                          href="/forgot-password"
                        >
                          Forgot Password?
                        </Link>
                      </div>
                      <Input
                        id="password"
                        type="password"
                        name="password"
                        placeholder="Your password"
                        required
                        className="w-full border-stone-300 focus:border-[#8B4513] focus:ring-[#8B4513]/20 transition-all"
                      />
                    </div>
                  </div>

                  <SubmitButton
                    className="w-full bg-[#8B4513] text-white hover:bg-[#A0522D] rounded-md py-2.5 font-medium shadow-sm transition-all duration-200 hover:shadow"
                    pendingText="Signing in..."
                    formAction={signInAction}
                  >
                    Sign in
                  </SubmitButton>

                  <div className="relative">
                    <div className="absolute inset-0 flex items-center">
                      <span className="w-full border-t border-stone-200" />
                    </div>
                    <div className="relative flex justify-center text-xs uppercase">
                      <span className="bg-white px-2 text-stone-500">
                        Or continue with
                      </span>
                    </div>
                  </div>

                  <GoogleSignInButton />

                  <FormMessage message={message} />
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
