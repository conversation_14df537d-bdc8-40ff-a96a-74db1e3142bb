'use client'

import { useState } from 'react';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Lock, Check, Sparkles } from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { createClient } from '../../supabase/client';
import { getBorderOptions, updateUserProfile, type BorderOption } from '@/utils/profile-utils';

interface BorderSelectorProps {
  currentAvatar: string | null;
  currentBorder: string | null;
  userLevel: number;
  userId: string;
  displayName: string;
  onBorderChange?: (newBorder: string) => void;
}

export default function BorderSelector({ 
  currentAvatar, 
  currentBorder, 
  userLevel, 
  userId, 
  displayName,
  onBorderChange 
}: BorderSelectorProps) {
  const [selectedBorder, setSelectedBorder] = useState<string | null>(currentBorder);
  const [isUpdating, setIsUpdating] = useState(false);
  const borderOptions = getBorderOptions(userLevel);

  const handleBorderSelect = async (option: BorderOption) => {
    if (option.locked) {
      toast.error(`Border locked! ${option.unlock_condition}`);
      return;
    }

    setIsUpdating(true);
    try {
      const success = await updateUserProfile(userId, { avatar_border: option.style });
      
      if (success) {
        setSelectedBorder(option.style);
        onBorderChange?.(option.style);
        toast.success(`Border changed to ${option.name}!`);
        
        // Refresh the page to update the avatar in the navbar
        window.location.reload();
      } else {
        toast.error('Failed to update border. Please try again.');
      }
    } catch (error) {
      console.error('Error updating border:', error);
      toast.error('An error occurred while updating your border.');
    } finally {
      setIsUpdating(false);
    }
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'border-gray-300 bg-gray-50';
      case 'rare': return 'border-blue-300 bg-blue-50';
      case 'epic': return 'border-purple-300 bg-purple-50';
      case 'legendary': return 'border-yellow-300 bg-yellow-50';
      default: return 'border-gray-300 bg-gray-50';
    }
  };

  return (
    <div>
      <div className="flex items-center justify-between mb-3">
        <h3 className="font-medium text-amber-900 text-sm sm:text-base">Choose Avatar Border</h3>
        <Badge className="bg-amber-100 text-amber-800">
          Level {userLevel}
        </Badge>
      </div>
      
      {/* Preview */}
      <div className="flex justify-center mb-6">
        <div className={cn(
          "p-1 rounded-full",
          selectedBorder?.includes('bg-gradient') ? selectedBorder : ''
        )}>
          <Avatar className={cn(
            "h-24 w-24",
            !selectedBorder?.includes('bg-gradient') ? selectedBorder : ''
          )}>
            <AvatarImage src={currentAvatar || ''} alt={displayName} />
            <AvatarFallback className="bg-amber-200 text-amber-800 text-xl">
              {displayName.substring(0, 2).toUpperCase()}
            </AvatarFallback>
          </Avatar>
        </div>
      </div>
      
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
        {borderOptions.map((option) => {
          const isSelected = selectedBorder === option.style;
          const hasGradient = option.style.includes('bg-gradient');

          return (
            <div
              key={option.id}
              className={cn(
                "flex flex-col items-center cursor-pointer p-2 rounded-lg transition-all duration-200",
                option.locked 
                  ? "opacity-50 cursor-not-allowed" 
                  : "hover:bg-gray-50 hover:scale-105",
                isSelected && "ring-2 ring-amber-400 bg-amber-50",
                isUpdating && "pointer-events-none opacity-75",
                getRarityColor(option.rarity)
              )}
              onClick={() => handleBorderSelect(option)}
            >
              <div className="relative">
                <div className={cn(
                  "p-1 rounded-full",
                  hasGradient ? option.style : ''
                )}>
                  <Avatar className={cn(
                    "h-12 w-12 sm:h-16 sm:w-16",
                    !hasGradient ? option.style : ''
                  )}>
                    <AvatarImage
                      src={currentAvatar || ''}
                      alt={option.name}
                    />
                    <AvatarFallback className="bg-amber-200 text-amber-800 text-xs sm:text-sm">
                      {displayName.substring(0, 2).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                </div>
                
                {option.locked && (
                  <div className="absolute inset-0 flex items-center justify-center bg-black/50 rounded-full">
                    <Lock size={16} className="text-white" />
                  </div>
                )}
                
                {isSelected && !option.locked && (
                  <div className="absolute -top-1 -right-1 bg-amber-500 rounded-full p-1">
                    <Check size={12} className="text-white" />
                  </div>
                )}
                
                {/* Rarity indicator */}
                <div className={cn(
                  "absolute -bottom-1 left-1/2 transform -translate-x-1/2 px-1 py-0.5 rounded text-[8px] font-bold",
                  option.rarity === 'common' && "bg-gray-200 text-gray-700",
                  option.rarity === 'rare' && "bg-blue-200 text-blue-700",
                  option.rarity === 'epic' && "bg-purple-200 text-purple-700",
                  option.rarity === 'legendary' && "bg-yellow-200 text-yellow-700"
                )}>
                  {option.rarity.charAt(0).toUpperCase()}
                </div>
              </div>
              
              <span className="text-[10px] sm:text-xs mt-1 text-center text-amber-800 font-medium">
                {option.name}
              </span>
              
              {option.locked && option.unlock_condition && (
                <span className="text-[8px] text-gray-500 text-center mt-0.5">
                  {option.unlock_condition}
                </span>
              )}
            </div>
          );
        })}
      </div>
      
      <div className="mt-4 p-3 bg-amber-50 rounded-lg border border-amber-200">
        <div className="flex items-center gap-2 mb-2">
          <Sparkles className="h-4 w-4 text-amber-600" />
          <span className="text-sm font-medium text-amber-900">Border Rarity Guide</span>
        </div>
        <div className="grid grid-cols-2 gap-2 text-xs">
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 bg-gray-300 rounded"></div>
            <span>Common - Always available</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 bg-blue-300 rounded"></div>
            <span>Rare - Level rewards</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 bg-purple-300 rounded"></div>
            <span>Epic - Achievements</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 bg-yellow-300 rounded"></div>
            <span>Legendary - Special</span>
          </div>
        </div>
      </div>
      
      {isUpdating && (
        <div className="mt-3 text-center">
          <div className="inline-flex items-center gap-2 text-sm text-amber-700">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-amber-700"></div>
            Updating border...
          </div>
        </div>
      )}
    </div>
  );
}
