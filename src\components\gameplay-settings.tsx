"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Sun, Moon } from "lucide-react";
import { useState } from "react";
import { AudioSettings } from "./audio-settings";

interface GameplaySettingsProps {
  initialDarkMode: boolean;
}

export function GameplaySettings({ initialDarkMode }: GameplaySettingsProps) {
  const [darkMode, setDarkMode] = useState(initialDarkMode);

  return (
    <Card className="border-amber-200 shadow-sm bg-white">
      {/* Audio Settings */}
      <AudioSettings />

      {/* Visual Settings */}
      <CardHeader className="pb-2 pt-4 border-t border-amber-100">
        <CardTitle className="text-base sm:text-lg text-amber-900">
          Visual Settings
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between p-3 rounded-md border border-amber-200">
          <div className="flex items-center gap-2">
            {darkMode ? (
              <Moon size={16} className="text-amber-700" />
            ) : (
              <Sun size={16} className="text-amber-700" />
            )}
            <span className="text-sm text-amber-900">
              {darkMode ? "Night Owl" : "Sunlit Pages"}
            </span>
          </div>
          <Switch
            checked={darkMode}
            onCheckedChange={setDarkMode}
            className="data-[state=checked]:bg-amber-600"
          />
        </div>
      </CardContent>
    </Card>
  );
}
