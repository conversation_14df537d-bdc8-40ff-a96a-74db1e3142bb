import { createClient } from "../../../../supabase/server";
import { NextResponse } from "next/server";

export async function GET(request: Request) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get("code");
  const type = requestUrl.searchParams.get("type");
  const redirect_to = requestUrl.searchParams.get("redirect_to");

  const supabase = await createClient();

  if (code) {
    // Exchange the code for a session
    await supabase.auth.exchangeCodeForSession(code);

    // Check if this is a password reset flow
    if (type === "recovery") {
      // Redirect to the password reset page
      return NextResponse.redirect(new URL("/reset-password", requestUrl.origin));
    }
  }

  // For normal sign-in flow, redirect to the intended destination
  const redirectTo = redirect_to || "/dashboard";
  return NextResponse.redirect(new URL(redirectTo, requestUrl.origin));
}