import { resetPasswordAction } from "@/app/actions";
import { FormMessage, Message } from "@/components/form-message";
import Navbar from "@/components/navbar";
import { SubmitButton } from "@/components/submit-button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { createClient } from "../../../../supabase/server";
import { redirect } from "next/navigation";
import Image from "next/image";

export default async function ResetPassword(props: {
  searchParams: Promise<Message>;
}) {
  const searchParams = await props.searchParams;

  // Check for error messages first
  if ("message" in searchParams) {
    return (
      <div className="flex h-screen w-full flex-1 items-center justify-center p-4 sm:max-w-md">
        <FormMessage message={searchParams} />
      </div>
    );
  }

  // Verify if we have a valid recovery token
  const supabase = await createClient();
  const { data: { session }, error: sessionError } = await supabase.auth.getSession();
  if (!session) {
    return redirect("/sign-in?error=Invalid or expired recovery link. Please try requesting a new password reset.");
  }

  return (
    <>
      <div className="flex min-h-screen bg-[#fff8e8]">
        {/* Left panel - decorative */}
        <div className="hidden md:flex md:w-1/2 bg-gradient-to-br from-[#8B4513] to-[#A0522D] flex-col items-center justify-center p-12 text-white">
          <div className="w-full max-w-md space-y-6">
            <div className="flex justify-center mb-8">
              <Image src="/logo2.png" alt="Word Nook Logo" width={180} height={60} className="drop-shadow-md bg-[#fff8e8] rounded-lg" />
            </div>
            <h2 className="text-3xl font-bold tracking-tight text-center">Create New Password</h2>
            <p className="text-lg text-center opacity-90">Set a new password to secure your account.</p>
            <div className="mt-8 p-6 bg-white/10 rounded-lg backdrop-blur-sm border border-white/20 shadow-xl">
              <p className="italic text-white/90">"In the end, we will remember not the words of our enemies, but the silence of our friends."</p>
              <p className="mt-2 text-right text-white/80">— Martin Luther King Jr.</p>
            </div>
          </div>
        </div>

        {/* Right panel - form */}
        <div className="w-full md:w-1/2 flex items-center justify-center p-4 md:p-12">
          <div className="w-full max-w-md">
            <div className="md:hidden flex justify-center mb-8">
              <Image src="/logo2.png" alt="Word Nook Logo" width={150} height={50} />
            </div>

            <div className="bg-white rounded-xl border border-stone-200 shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl">
              <div className="p-8">
                <form className="flex flex-col space-y-6">
                  <div className="space-y-2">
                    <h1 className="text-3xl font-bold tracking-tight text-stone-800">Reset Password</h1>
                    <p className="text-sm text-stone-500">
                      Please enter your new password below.
                    </p>
                  </div>

                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="password" className="text-sm font-medium text-stone-700">
                        New password
                      </Label>
                      <Input
                        id="password"
                        type="password"
                        name="password"
                        placeholder="New password"
                        required
                        minLength={6}
                        className="w-full border-stone-300 focus:border-[#8B4513] focus:ring-[#8B4513]/20 transition-all"
                      />
                      <p className="text-xs text-stone-500 mt-1">Password must be at least 6 characters long</p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="confirmPassword" className="text-sm font-medium text-stone-700">
                        Confirm password
                      </Label>
                      <Input
                        id="confirmPassword"
                        type="password"
                        name="confirmPassword"
                        placeholder="Confirm password"
                        required
                        minLength={6}
                        className="w-full border-stone-300 focus:border-[#8B4513] focus:ring-[#8B4513]/20 transition-all"
                      />
                    </div>
                  </div>

                  <SubmitButton
                    formAction={resetPasswordAction}
                    pendingText="Resetting password..."
                    className="w-full bg-[#8B4513] text-white hover:bg-[#A0522D] rounded-md py-2.5 font-medium shadow-sm transition-all duration-200 hover:shadow"
                  >
                    Reset Password
                  </SubmitButton>

                  <FormMessage message={searchParams} />
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
