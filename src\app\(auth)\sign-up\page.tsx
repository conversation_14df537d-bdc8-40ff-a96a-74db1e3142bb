import { FormMessage, Message } from "@/components/form-message";
import { SubmitButton } from "@/components/submit-button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Link from "next/link";
import { SmtpMessage } from "../smtp-message";
import { signInWithGoogleAction, signUpAction } from "@/app/actions";
import Navbar from "@/components/navbar";
import { UrlProvider } from "@/components/url-provider";
import { GoogleSignInButton } from "@/components/ui/google-signin-button";
import Image from "next/image";

export default async function Signup(props: {
  searchParams: Promise<Message>;
}) {
  const searchParams = await props.searchParams;
  if ("message" in searchParams) {
    return (
      <div className="flex h-screen w-full flex-1 items-center justify-center p-4 sm:max-w-md">
        <FormMessage message={searchParams} />
      </div>
    );
  }

  return (
    <>
      <div className="flex min-h-screen bg-[#fff8e8]">
        {/* Left panel - decorative */}
        <div className="hidden md:flex md:w-1/2 bg-gradient-to-br from-[#8B4513] to-[#A0522D] flex-col items-center justify-center p-12 text-white">
          <div className="w-full max-w-md space-y-6">
            <div className="flex justify-center mb-8">
              <Image src="/logo2.png" alt="Word Nook Logo" width={180} height={60} className="drop-shadow-md bg-[#fff8e8] rounded-lg" />
            </div>
            <h2 className="text-3xl font-bold tracking-tight text-center">Join Word Nook Today</h2>
            <p className="text-lg text-center opacity-90">Begin your adventure in the world of words.</p>
            <div className="mt-8 p-6 bg-white/10 rounded-lg backdrop-blur-sm border border-white/20 shadow-xl">
              <p className="italic text-white/90">"Words are, in my not-so-humble opinion, our most inexhaustible source of magic."</p>
              <p className="mt-2 text-right text-white/80">— J.K. Rowling</p>
            </div>
          </div>
        </div>

        {/* Right panel - form */}
        <div className="w-full md:w-1/2 flex items-center justify-center p-4 md:p-12">
          <div className="w-full max-w-md">
            <div className="md:hidden flex justify-center mb-8">
              <Image src="/logo2.png" alt="Word Nook Logo" width={150} height={50} />
            </div>

            <div className="bg-white rounded-xl border border-stone-200 shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl">
              <div className="p-8">
                <UrlProvider>
                  <form className="flex flex-col space-y-6">
                    <div className="space-y-2">
                      <h1 className="text-3xl font-bold tracking-tight text-stone-800">Sign up</h1>
                      <p className="text-sm text-stone-500">
                        Already have an account?{" "}
                        <Link
                          className="text-[#8B4513] font-medium hover:underline transition-all"
                          href="/sign-in"
                        >
                          Sign in
                        </Link>
                      </p>
                    </div>

                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="full_name" className="text-sm font-medium text-stone-700">
                          Name
                        </Label>
                        <Input
                          id="full_name"
                          name="full_name"
                          type="text"
                          placeholder="John Doe"
                          required
                          className="w-full border-stone-300 focus:border-[#8B4513] focus:ring-[#8B4513]/20 transition-all"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="email" className="text-sm font-medium text-stone-700">
                          Email
                        </Label>
                        <Input
                          id="email"
                          name="email"
                          type="email"
                          placeholder="<EMAIL>"
                          required
                          className="w-full border-stone-300 focus:border-[#8B4513] focus:ring-[#8B4513]/20 transition-all"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="password" className="text-sm font-medium text-stone-700">
                          Password
                        </Label>
                        <Input
                          id="password"
                          type="password"
                          name="password"
                          placeholder="Your password (min. 6 characters)"
                          minLength={6}
                          required
                          className="w-full border-stone-300 focus:border-[#8B4513] focus:ring-[#8B4513]/20 transition-all"
                        />
                        <p className="text-xs text-stone-500 mt-1">Password must be at least 6 characters long</p>
                      </div>
                    </div>

                    <SubmitButton
                      formAction={signUpAction}
                      pendingText="Signing up..."
                      className="w-full bg-[#8B4513] text-white hover:bg-[#A0522D] rounded-md py-2.5 font-medium shadow-sm transition-all duration-200 hover:shadow"
                    >
                      Create account
                    </SubmitButton>

                    <div className="relative">
                      <div className="absolute inset-0 flex items-center">
                        <span className="w-full border-t border-stone-200" />
                      </div>
                      <div className="relative flex justify-center text-xs uppercase">
                        <span className="bg-white px-2 text-stone-500">
                          Or continue with
                        </span>
                      </div>
                    </div>

                    <GoogleSignInButton />

                    <FormMessage message={searchParams} />
                  </form>
                </UrlProvider>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
