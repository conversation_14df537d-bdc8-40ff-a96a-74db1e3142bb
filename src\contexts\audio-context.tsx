"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// Define the shape of our audio context
interface AudioContextType {
  wordPronunciationVolume: number;
  setWordPronunciationVolume: (volume: number) => void;
  backgroundMusicVolume: number;
  setBackgroundMusicVolume: (volume: number) => void;
  soundEffectsVolume: number;
  setSoundEffectsVolume: (volume: number) => void;
  isMuted: boolean;
  setIsMuted: (muted: boolean) => void;
  toggleMute: () => void;
}

// Create the context with default values
const AudioContext = createContext<AudioContextType>({
  wordPronunciationVolume: 0.8,
  setWordPronunciationVolume: () => {},
  backgroundMusicVolume: 0.5,
  setBackgroundMusicVolume: () => {},
  soundEffectsVolume: 0.6,
  setSoundEffectsVolume: () => {},
  isMuted: false,
  setIsMuted: () => {},
  toggleMute: () => {},
});

// Storage keys for persisting audio settings
const STORAGE_KEYS = {
  WORD_VOLUME: 'wordnook_word_volume',
  BG_MUSIC_VOLUME: 'wordnook_bg_music_volume',
  SOUND_EFFECTS_VOLUME: 'wordnook_sound_effects_volume',
  IS_MUTED: 'wordnook_is_muted',
};

interface AudioProviderProps {
  children: ReactNode;
}

export const AudioProvider: React.FC<AudioProviderProps> = ({ children }) => {
  // Initialize state with default values or from localStorage if available
  const [wordPronunciationVolume, setWordPronunciationVolume] = useState<number>(0.8);
  const [backgroundMusicVolume, setBackgroundMusicVolume] = useState<number>(0.5);
  const [soundEffectsVolume, setSoundEffectsVolume] = useState<number>(0.6);
  const [isMuted, setIsMuted] = useState<boolean>(false);

  // Load saved settings from localStorage on initial render
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Word pronunciation volume
      const savedWordVolume = localStorage.getItem(STORAGE_KEYS.WORD_VOLUME);
      if (savedWordVolume !== null) {
        setWordPronunciationVolume(parseFloat(savedWordVolume));
      }

      // Background music volume
      const savedBgMusicVolume = localStorage.getItem(STORAGE_KEYS.BG_MUSIC_VOLUME);
      if (savedBgMusicVolume !== null) {
        setBackgroundMusicVolume(parseFloat(savedBgMusicVolume));
      }

      // Sound effects volume
      const savedSoundEffectsVolume = localStorage.getItem(STORAGE_KEYS.SOUND_EFFECTS_VOLUME);
      if (savedSoundEffectsVolume !== null) {
        setSoundEffectsVolume(parseFloat(savedSoundEffectsVolume));
      }

      // Mute state
      const savedMuteState = localStorage.getItem(STORAGE_KEYS.IS_MUTED);
      if (savedMuteState !== null) {
        setIsMuted(savedMuteState === 'true');
      }
    }
  }, []);

  // Save settings to localStorage when they change
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem(STORAGE_KEYS.WORD_VOLUME, wordPronunciationVolume.toString());
      localStorage.setItem(STORAGE_KEYS.BG_MUSIC_VOLUME, backgroundMusicVolume.toString());
      localStorage.setItem(STORAGE_KEYS.SOUND_EFFECTS_VOLUME, soundEffectsVolume.toString());
      localStorage.setItem(STORAGE_KEYS.IS_MUTED, isMuted.toString());
    }
  }, [wordPronunciationVolume, backgroundMusicVolume, soundEffectsVolume, isMuted]);

  // Toggle mute function
  const toggleMute = () => {
    setIsMuted(prev => !prev);
  };

  // Create the context value object
  const contextValue: AudioContextType = {
    wordPronunciationVolume,
    setWordPronunciationVolume,
    backgroundMusicVolume,
    setBackgroundMusicVolume,
    soundEffectsVolume,
    setSoundEffectsVolume,
    isMuted,
    setIsMuted,
    toggleMute,
  };

  return (
    <AudioContext.Provider value={contextValue}>
      {children}
    </AudioContext.Provider>
  );
};

// Custom hook to use the audio context
export const useAudio = () => useContext(AudioContext);

export default AudioContext;
