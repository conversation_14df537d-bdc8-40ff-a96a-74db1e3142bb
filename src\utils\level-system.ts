import { createClient } from "../../supabase/client";

export interface LevelInfo {
  currentLevel: number;
  currentXP: number;
  xpForCurrentLevel: number;
  xpForNextLevel: number;
  xpToNextLevel: number;
  progressPercentage: number;
}

export interface ExperienceGain {
  baseXP: number;
  rankBonus: number;
  performanceBonus: number;
  difficultyMultiplier: number;
  totalXP: number;
  breakdown: string[];
}

// Difficulty multipliers for XP gain
const DIFFICULTY_MULTIPLIERS = {
  easy: 1.0,
  medium: 1.2,
  hard: 1.5,
  extreme: 2.0
};

/**
 * Calculate XP required for a specific level
 * Uses exponential growth: Level n requires (n-1)² × 50 + (n-1) × 50 total XP
 */
export function getXPRequiredForLevel(level: number): number {
  if (level <= 1) return 0;
  
  let totalXP = 0;
  for (let i = 2; i <= level; i++) {
    // Each level requires more XP than the previous: 100, 150, 200, 250, 300, etc.
    const xpForThisLevel = 50 + (i - 1) * 50;
    totalXP += xpForThisLevel;
  }
  return totalXP;
}

/**
 * Calculate current level from total XP
 */
export function getLevelFromXP(totalXP: number): number {
  let level = 1;
  while (getXPRequiredForLevel(level + 1) <= totalXP) {
    level++;
  }
  return level;
}

/**
 * Get detailed level information from total XP
 */
export function getLevelInfo(totalXP: number): LevelInfo {
  const currentLevel = getLevelFromXP(totalXP);
  const xpForCurrentLevel = getXPRequiredForLevel(currentLevel);
  const xpForNextLevel = getXPRequiredForLevel(currentLevel + 1);
  const xpToNextLevel = xpForNextLevel - totalXP;
  const xpInCurrentLevel = totalXP - xpForCurrentLevel;
  const xpNeededForCurrentLevel = xpForNextLevel - xpForCurrentLevel;
  const progressPercentage = Math.round((xpInCurrentLevel / xpNeededForCurrentLevel) * 100);

  return {
    currentLevel,
    currentXP: totalXP,
    xpForCurrentLevel,
    xpForNextLevel,
    xpToNextLevel,
    progressPercentage: Math.min(100, Math.max(0, progressPercentage))
  };
}

/**
 * Calculate experience gained from a match
 */
export function calculateMatchXP(
  finalRank: number,
  totalPlayers: number,
  score: number,
  difficulty: string,
  eliminationRound?: number
): ExperienceGain {
  // Base XP for participating
  const baseXP = 10;
  
  // Rank bonus: better rank = more XP
  const rankBonus = (totalPlayers - finalRank + 1) * 5;
  
  // Performance bonus based on score
  const performanceBonus = Math.floor(score / 100);
  
  // Survival bonus if not eliminated early
  const survivalBonus = eliminationRound ? 0 : 15;
  
  // Calculate subtotal before difficulty multiplier
  const subtotal = baseXP + rankBonus + performanceBonus + survivalBonus;
  
  // Apply difficulty multiplier
  const difficultyMultiplier = DIFFICULTY_MULTIPLIERS[difficulty.toLowerCase() as keyof typeof DIFFICULTY_MULTIPLIERS] || 1.0;
  const totalXP = Math.round(subtotal * difficultyMultiplier);
  
  // Create breakdown for display
  const breakdown = [
    `Base participation: +${baseXP} XP`,
    `Rank bonus (#${finalRank}/${totalPlayers}): +${rankBonus} XP`,
    `Performance bonus: +${performanceBonus} XP`,
  ];
  
  if (survivalBonus > 0) {
    breakdown.push(`Survival bonus: +${survivalBonus} XP`);
  }
  
  if (difficultyMultiplier !== 1.0) {
    breakdown.push(`${difficulty} difficulty: ×${difficultyMultiplier}`);
  }

  return {
    baseXP,
    rankBonus,
    performanceBonus,
    difficultyMultiplier,
    totalXP,
    breakdown
  };
}

/**
 * Award experience to a player and update their level
 */
export async function awardExperience(
  playerId: string,
  xpGained: number
): Promise<{ success: boolean; newLevel?: number; leveledUp?: boolean }> {
  const supabase = createClient();

  try {
    // Get current player data
    const { data: playerData, error: fetchError } = await supabase
      .from('players')
      .select('level_experience')
      .eq('id', playerId)
      .single();

    if (fetchError) {
      console.error('Error fetching player data:', fetchError);
      return { success: false };
    }

    const currentXP = playerData?.level_experience || 0;
    const oldLevel = getLevelFromXP(currentXP);
    const newTotalXP = currentXP + xpGained;
    const newLevel = getLevelFromXP(newTotalXP);
    const leveledUp = newLevel > oldLevel;

    // Update player's XP
    const { error: updateError } = await supabase
      .from('players')
      .update({ level_experience: newTotalXP })
      .eq('id', playerId);

    if (updateError) {
      console.error('Error updating player XP:', updateError);
      return { success: false };
    }

    return {
      success: true,
      newLevel,
      leveledUp
    };

  } catch (error) {
    console.error('Exception in awardExperience:', error);
    return { success: false };
  }
}

/**
 * Get level leaderboard (top players by level and XP)
 */
export async function getLevelLeaderboard(limit: number = 10) {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('players')
      .select('id, display_name, avatar_url, avatar_border, level_experience')
      .order('level_experience', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching level leaderboard:', error);
      return [];
    }

    return data?.map(player => ({
      ...player,
      levelInfo: getLevelInfo(player.level_experience || 0)
    })) || [];

  } catch (error) {
    console.error('Exception in getLevelLeaderboard:', error);
    return [];
  }
}

/**
 * Get XP milestones for UI display
 */
export function getXPMilestones(): { level: number; xp: number; rewards: string[] }[] {
  const milestones = [];
  
  for (let level = 1; level <= 20; level++) {
    const rewards = [];
    
    // Add rewards based on level
    if (level === 5) rewards.push('Rare Avatars', 'Rare Backgrounds');
    if (level === 8) rewards.push('Professor Avatar');
    if (level === 10) rewards.push('Special Badge');
    if (level === 15) rewards.push('Epic Avatars', 'Epic Backgrounds');
    if (level === 20) rewards.push('Spell Master Avatar');
    if (level === 25) rewards.push('Epic Borders');
    if (level === 50) rewards.push('Legendary Content');
    
    milestones.push({
      level,
      xp: getXPRequiredForLevel(level),
      rewards
    });
  }
  
  return milestones;
}

/**
 * Format XP number for display
 */
export function formatXP(xp: number): string {
  if (xp >= 1000000) {
    return `${(xp / 1000000).toFixed(1)}M XP`;
  }
  if (xp >= 1000) {
    return `${(xp / 1000).toFixed(1)}K XP`;
  }
  return `${xp} XP`;
}

/**
 * Get level badge/title based on level
 */
export function getLevelTitle(level: number): string {
  if (level >= 100) return 'Grandmaster';
  if (level >= 75) return 'Legend';
  if (level >= 50) return 'Master';
  if (level >= 25) return 'Expert';
  if (level >= 15) return 'Advanced';
  if (level >= 10) return 'Skilled';
  if (level >= 5) return 'Apprentice';
  return 'Novice';
}
