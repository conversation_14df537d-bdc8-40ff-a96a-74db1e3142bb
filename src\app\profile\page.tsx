import React from 'react';
import { createClient } from '../../../supabase/server';
import { redirect } from 'next/navigation';
import DashboardNavbar from "@/components/dashboard-navbar";
import {
  Trophy,
  BookOpen,
  Clock,
  Flame,
  Award,
  Settings,
  Calendar,
  BarChart4,
  Star,
  Volume2,
  VolumeX,
  Edit,
  Crown
} from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { cn } from "@/lib/utils";

export default async function ProfilePage() {
  const supabase = await createClient();

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return redirect("/sign-in");
  }

  // Mock data for the profile page
  const profileData = {
    username: "MOHAYMEN OMAR",
    title: "SpellMaster ⭐ | Undefeated",
    joinDate: "March 2024",
    avatar: "https://lh3.googleusercontent.com/a/ACg8ocJXxAYoLL8jr6Thv6ME3C_l5zknqDfUSQGRUPOg_PcpG-_wvLo0=s96-c",
    stats: {
      gamesPlayed: 42,
      wins: 15,
      winRate: 35,
      highestScore: 8500,
      longestStreak: 12
    },
    badges: [
      { name: "Boss Slayer", icon: Trophy, unlocked: true, description: "Defeat a boss level word" },
      { name: "Speed Demon", icon: Clock, unlocked: true, description: "Spell 5 words in under 5 seconds each" },
      { name: "Word Wizard", icon: BookOpen, unlocked: true, description: "Spell 50 different words correctly" },
      { name: "Streak Master", icon: Flame, unlocked: true, description: "Maintain a 10-word streak" },
      { name: "Perfect Game", icon: Star, unlocked: false, description: "Complete a game with no mistakes" },
      { name: "Vocabulary King", icon: Crown, unlocked: false, description: "Master 100 unique words" }
    ],
    recentActivity: [
      { action: "Played 'Expert'", result: "3rd place 🥉", time: "2 hours ago" },
      { action: "Unlocked 'Fast Fingers' badge", result: "", time: "1 day ago" },
      { action: "Won 'Medium' difficulty game", result: "1st place 🥇", time: "3 days ago" },
      { action: "Joined Word Nook", result: "", time: "1 month ago" }
    ],
    favoriteWords: [
      { word: "Quintessential", time: 13 },
      { word: "Phenomenon", time: 8 },
      { word: "Serendipity", time: 11 },
      { word: "Eloquent", time: 6 },
      { word: "Ephemeral", time: 9 }
    ],
    settings: {
      audioEnabled: true,
      backgroundMusicEnabled: false
    }
  };

  // Avatar options for customization
  const avatarOptions = [
    { name: "Book", seed: "Book" },
    { name: "Mug", seed: "Mug" },
    { name: "Owl", seed: "Owl" },
    { name: "Cat", seed: "Cat" },
    { name: "Fox", seed: "Fox" }
  ];

  return (
    <>
      <DashboardNavbar />
      <main className="w-full bg-amber-50/30 min-h-screen">
        <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-8 flex flex-col gap-4 sm:gap-6 md:gap-8">
          {/* Header Section */}
          <header className="flex flex-col gap-2 sm:gap-4">
            <h1 className="text-2xl sm:text-3xl font-bold text-amber-900">My Profile</h1>
            <div className="bg-amber-100 text-xs sm:text-sm p-2 sm:p-3 px-3 sm:px-4 rounded-lg text-amber-800 flex gap-2 items-center border border-amber-200">
              <BookOpen size={14} className="hidden xs:inline" />
              <BookOpen size={12} className="xs:hidden" />
              <span>View and customize your Word Nook profile</span>
            </div>
          </header>

          {/* Profile Overview */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Left Column - Profile Card */}
            <div className="lg:col-span-1">
              <Card className="bg-white border shadow-sm overflow-hidden">
                <CardHeader className="bg-amber-100 pb-4 sm:pb-6 relative">
                  <div className="absolute top-2 sm:top-4 right-2 sm:right-4">
                    <Button variant="ghost" size="icon" className="h-7 w-7 sm:h-8 sm:w-8 rounded-full bg-white/80 hover:bg-white">
                      <Edit size={14} className="text-amber-800 sm:hidden" />
                      <Edit size={16} className="text-amber-800 hidden sm:inline" />
                    </Button>
                  </div>
                  <div className="flex flex-col items-center">
                    <Avatar className="h-16 w-16 sm:h-20 sm:w-20 md:h-24 md:w-24 border-2 sm:border-4 border-white shadow-md">
                      <AvatarImage src={profileData.avatar} alt={profileData.username} />
                      <AvatarFallback className="bg-amber-200 text-amber-800 text-sm sm:text-base md:text-xl">
                        {profileData.username.substring(0, 2).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <CardTitle className="mt-3 sm:mt-4 text-xl sm:text-2xl font-bold text-amber-900">
                      {profileData.username}
                    </CardTitle>
                    <CardDescription className="text-amber-700 font-medium text-sm sm:text-base text-center px-2">
                      {profileData.title}
                    </CardDescription>
                  </div>
                </CardHeader>
                <CardContent className="pt-4 sm:pt-6 px-3 sm:px-6">
                  <div className="flex items-center gap-2 mb-4 text-amber-800">
                    <Calendar size={14} className="sm:hidden" />
                    <Calendar size={16} className="hidden sm:inline" />
                    <span className="text-xs sm:text-sm">Member since {profileData.joinDate}</span>
                  </div>

                  <div className="space-y-3 sm:space-y-4 mt-4 sm:mt-6">
                    <h3 className="font-semibold text-amber-900 flex items-center gap-2 text-sm sm:text-base">
                      <BarChart4 size={16} className="text-amber-700 sm:hidden" />
                      <BarChart4 size={18} className="text-amber-700 hidden sm:inline" />
                      Stats Overview
                    </h3>

                    <div className="space-y-3">
                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-amber-800">Games Played</span>
                          <span className="font-medium text-amber-900">{profileData.stats.gamesPlayed}</span>
                        </div>
                        <div className="h-2 bg-amber-100 rounded-full overflow-hidden">
                          <div className="h-full bg-amber-500" style={{ width: '100%' }}></div>
                        </div>
                      </div>

                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-amber-800">Wins ({profileData.stats.winRate}% win rate)</span>
                          <span className="font-medium text-amber-900">{profileData.stats.wins}</span>
                        </div>
                        <div className="h-2 bg-amber-100 rounded-full overflow-hidden">
                          <div className="h-full bg-green-500" style={{ width: `${profileData.stats.winRate}%` }}></div>
                        </div>
                      </div>

                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-amber-800">Highest Score</span>
                          <span className="font-medium text-amber-900">{profileData.stats.highestScore.toLocaleString()}</span>
                        </div>
                        <div className="h-2 bg-amber-100 rounded-full overflow-hidden">
                          <div className="h-full bg-blue-500" style={{ width: '85%' }}></div>
                        </div>
                      </div>

                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-amber-800">Longest Streak</span>
                          <span className="font-medium text-amber-900">{profileData.stats.longestStreak} words</span>
                        </div>
                        <div className="h-2 bg-amber-100 rounded-full overflow-hidden">
                          <div className="h-full bg-red-500" style={{ width: '60%' }}></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Right Column - Tabs for different sections */}
            <div className="lg:col-span-2">
              <Tabs defaultValue="badges" className="w-full">
                <TabsList className="grid grid-cols-4 sm:mb-6 bg-amber-100/50 p-1 sm:p-2 rounded-lg h-auto">
                  <TabsTrigger value="badges" className="data-[state=active]:bg-amber-600 text-xs sm:text-sm py-1.5 sm:py-2">
                    <Award size={14} className="mr-1 sm:hidden" />
                    <span className="xs:inline">Badges</span>
                    <span className="xs:hidden">🏆</span>
                  </TabsTrigger>
                  <TabsTrigger value="activity" className="data-[state=active]:bg-amber-600 text-xs sm:text-sm py-1.5 sm:py-2">
                    <Clock size={14} className="mr-1 sm:hidden" />
                    <span className=" xs:inline">Activity</span>
                    <span className="xs:hidden">📋</span>
                  </TabsTrigger>
                  <TabsTrigger value="words" className="data-[state=active]:bg-amber-600 text-xs sm:text-sm py-1.5 sm:py-2">
                    <BookOpen size={14} className="mr-1 sm:hidden" />
                    <span className=" xs:inline">Words</span>
                    <span className="xs:hidden">📚</span>
                  </TabsTrigger>
                  <TabsTrigger value="settings" className="data-[state=active]:bg-amber-600 text-xs sm:text-sm py-1.5 sm:py-2">
                    <Settings size={14} className="mr-1 sm:hidden" />
                    <span className=" xs:inline">Settings</span>
                    <span className="xs:hidden">⚙️</span>
                  </TabsTrigger>
                </TabsList>

                {/* Badges & Achievements Tab */}
                <TabsContent value="badges" className="mt-0">
                  <Card className="border shadow-sm">
                    <CardHeader className="pb-2 px-3 sm:px-6">
                      <CardTitle className="text-lg sm:text-xl text-amber-900 flex items-center gap-2">
                        <Award className="h-4 w-4 sm:h-5 sm:w-5 text-amber-600" />
                        Badges & Achievements
                      </CardTitle>
                      <CardDescription className="text-xs sm:text-sm">
                        Unlock badges by completing special challenges
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="px-3 sm:px-6">
                      <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 gap-2 sm:gap-4 mt-2 sm:mt-4">
                        {profileData.badges.map((badge, index) => (
                          <div
                            key={index}
                            className={cn(
                              "flex flex-col items-center p-2 sm:p-4 rounded-lg border text-center",
                              badge.unlocked
                                ? "bg-amber-50 border-amber-200"
                                : "bg-gray-100 border-gray-200 opacity-70"
                            )}
                          >
                            <div className={cn(
                              "p-2 sm:p-3 rounded-full mb-1 sm:mb-2",
                              badge.unlocked ? "bg-amber-200" : "bg-gray-200"
                            )}>
                              <badge.icon
                                size={16}
                                className={cn(
                                  "sm:hidden",
                                  badge.unlocked ? "text-amber-700" : "text-gray-500"
                                )}
                              />
                              <badge.icon
                                size={24}
                                className={cn(
                                  "hidden sm:block",
                                  badge.unlocked ? "text-amber-700" : "text-gray-500"
                                )}
                              />
                            </div>
                            <h3 className={cn(
                              "font-medium mb-0.5 sm:mb-1 text-xs sm:text-sm",
                              badge.unlocked ? "text-amber-900" : "text-gray-600"
                            )}>
                              {badge.name}
                            </h3>
                            <p className="text-[10px] sm:text-xs text-amber-700 line-clamp-2">
                              {badge.unlocked ? "Unlocked" : badge.description}
                            </p>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Recent Activity Tab */}
                <TabsContent value="activity" className="mt-0">
                  <Card className="border shadow-sm">
                    <CardHeader className="pb-2 px-3 sm:px-6">
                      <CardTitle className="text-lg sm:text-xl text-amber-900 flex items-center gap-2">
                        <Clock className="h-4 w-4 sm:h-5 sm:w-5 text-amber-600" />
                        Recent Activity
                      </CardTitle>
                      <CardDescription className="text-xs sm:text-sm">
                        Your latest achievements and game results
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="px-3 sm:px-6">
                      <div className="space-y-2 sm:space-y-4 mt-2 sm:mt-4">
                        {profileData.recentActivity.map((activity, index) => (
                          <div
                            key={index}
                            className={cn(
                              "p-3 sm:p-4 rounded-lg border border-amber-100",
                              index === 0 ? "bg-amber-50" : "bg-white"
                            )}
                          >
                            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2 sm:gap-0">
                              <div>
                                <h3 className="font-medium text-amber-900 text-sm sm:text-base">{activity.action}</h3>
                                {activity.result && (
                                  <p className="text-amber-700 text-xs sm:text-sm mt-0.5 sm:mt-1">{activity.result}</p>
                                )}
                              </div>
                              <Badge variant="outline" className="text-[10px] sm:text-xs bg-amber-100/50 w-fit">
                                {activity.time}
                              </Badge>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Favorite Words Tab */}
                <TabsContent value="words" className="mt-0">
                  <Card className="border shadow-sm">
                    <CardHeader className="pb-2 px-3 sm:px-6">
                      <CardTitle className="text-lg sm:text-xl text-amber-900 flex items-center gap-2">
                        <BookOpen className="h-4 w-4 sm:h-5 sm:w-5 text-amber-600" />
                        Favorite Words
                      </CardTitle>
                      <CardDescription className="text-xs sm:text-sm">
                        Words you've spelled most often or fastest
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="px-3 sm:px-6">
                      <div className="mt-2 sm:mt-4">
                        <div className="rounded-lg border overflow-hidden">
                          <table className="w-full">
                            <thead className="bg-amber-100/50 text-amber-900">
                              <tr>
                                <th className="text-left p-2 sm:p-3 font-medium text-xs sm:text-sm">Word</th>
                                <th className="text-right p-2 sm:p-3 font-medium text-xs sm:text-sm">Time</th>
                                <th className="text-center p-2 sm:p-3 font-medium text-xs sm:text-sm w-10 sm:w-16">Play</th>
                              </tr>
                            </thead>
                            <tbody>
                              {profileData.favoriteWords.map((word, index) => (
                                <tr
                                  key={index}
                                  className={cn(
                                    "border-t border-amber-100",
                                    index % 2 === 0 ? "bg-amber-50/30" : "bg-white"
                                  )}
                                >
                                  <td className="p-2 sm:p-3 font-medium text-amber-900 text-xs sm:text-sm">{word.word}</td>
                                  <td className="p-2 sm:p-3 text-right text-amber-700 text-xs sm:text-sm">{word.time} sec</td>
                                  <td className="p-1 sm:p-3 text-center">
                                    <Button variant="ghost" size="sm" className="h-6 w-6 sm:h-8 sm:w-8 rounded-full p-0">
                                      <Volume2 size={14} className="text-amber-700 sm:hidden" />
                                      <Volume2 size={16} className="text-amber-700 hidden sm:inline" />
                                    </Button>
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Settings Tab */}
                <TabsContent value="settings" className="mt-0">
                  <Card className="border shadow-sm">
                    <CardHeader className="pb-2 px-3 sm:px-6">
                      <CardTitle className="text-lg sm:text-xl text-amber-900 flex items-center gap-2">
                        <Settings className="h-4 w-4 sm:h-5 sm:w-5 text-amber-600" />
                        Settings & Customization
                      </CardTitle>
                      <CardDescription className="text-xs sm:text-sm">
                        Personalize your Word Nook experience
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="px-3 sm:px-6">
                      <div className="space-y-4 sm:space-y-6 mt-2 sm:mt-4">
                        {/* Avatar Selection */}
                        <div>
                          <h3 className="font-medium text-amber-900 mb-2 sm:mb-3 text-sm sm:text-base">Choose Avatar</h3>
                          <div className="flex flex-wrap gap-2 sm:gap-4 justify-center sm:justify-start">
                            {avatarOptions.map((option, index) => (
                              <div
                                key={index}
                                className={cn(
                                  "flex flex-col items-center cursor-pointer",
                                  option.seed === "Book" ? "opacity-100" : "opacity-70 hover:opacity-100"
                                )}
                              >
                                <Avatar className="h-12 w-12 sm:h-16 sm:w-16 border-2 hover:border-amber-500 transition-all">
                                  <AvatarImage
                                    src={`https://api.dicebear.com/7.x/avataaars/svg?seed=${option.seed}`}
                                    alt={option.name}
                                  />
                                  <AvatarFallback className="bg-amber-200 text-amber-800 text-xs sm:text-sm">
                                    {option.name.substring(0, 1)}
                                  </AvatarFallback>
                                </Avatar>
                                <span className="text-[10px] sm:text-xs mt-1 sm:mt-2 text-amber-800">{option.name}</span>
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* Audio Settings */}
                        <div>
                          <h3 className="font-medium text-amber-900 mb-2 sm:mb-3 text-sm sm:text-base">Audio Settings</h3>
                          <div className="space-y-2 sm:space-y-3">
                            <div className="flex items-center justify-between p-2 sm:p-3 rounded-lg border border-amber-100 bg-white">
                              <div className="flex items-center gap-2 sm:gap-3">
                                <Volume2 size={14} className="text-amber-700 sm:hidden" />
                                <Volume2 size={18} className="text-amber-700 hidden sm:block" />
                                <span className="text-amber-900 text-xs sm:text-sm">Word Pronunciations</span>
                              </div>
                              <div className="relative inline-flex h-5 w-9 sm:h-6 sm:w-11 items-center rounded-full bg-amber-600">
                                <span className="absolute h-3 w-3 sm:h-4 sm:w-4 rounded-full bg-white transition-transform translate-x-[18px] sm:translate-x-6"></span>
                              </div>
                            </div>

                            <div className="flex items-center justify-between p-2 sm:p-3 rounded-lg border border-amber-100 bg-white">
                              <div className="flex items-center gap-2 sm:gap-3">
                                <VolumeX size={14} className="text-amber-700 sm:hidden" />
                                <VolumeX size={18} className="text-amber-700 hidden sm:block" />
                                <span className="text-amber-900 text-xs sm:text-sm">Background Music</span>
                              </div>
                              <div className="relative inline-flex h-5 w-9 sm:h-6 sm:w-11 items-center rounded-full bg-gray-300">
                                <span className="absolute h-3 w-3 sm:h-4 sm:w-4 rounded-full bg-white transition-transform translate-x-1"></span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                    <CardFooter className="flex justify-end pt-0 px-3 sm:px-6 pb-3 sm:pb-4">
                      <Button className="bg-amber-600 hover:bg-amber-700 text-white text-xs sm:text-sm h-8 sm:h-10">
                        Save Changes
                      </Button>
                    </CardFooter>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>
          </div>
        </div>
      </main>
    </>
  );
};
