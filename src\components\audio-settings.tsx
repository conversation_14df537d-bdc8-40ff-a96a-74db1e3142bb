"use client";

import { useState, useEffect } from "react";
import { Music, Volume2 } from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import { useAudio } from "@/contexts/audio-context";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";

export function AudioSettings() {
  const {
    wordPronunciationVolume,
    setWordPronunciationVolume,
    backgroundMusicVolume,
    setBackgroundMusicVolume,
    soundEffectsVolume,
    setSoundEffectsVolume,
    isMuted,
    setIsMuted
  } = useAudio();

  // Convert decimal volumes (0-1) to percentage (0-100) for UI
  const [wordVolume, setWordVolume] = useState(Math.round(wordPronunciationVolume * 100));
  const [bgMusicVolume, setBgMusicVolume] = useState(Math.round(backgroundMusicVolume * 100));
  const [effectsVolume, setEffectsVolume] = useState(Math.round(soundEffectsVolume * 100));

  // Update local state when context values change
  useEffect(() => {
    setWordVolume(Math.round(wordPronunciationVolume * 100));
    setBgMusicVolume(Math.round(backgroundMusicVolume * 100));
    setEffectsVolume(Math.round(soundEffectsVolume * 100));
  }, [wordPronunciationVolume, backgroundMusicVolume, soundEffectsVolume]);

  // Handle word pronunciation volume change
  const handleWordVolumeChange = (value: number[]) => {
    const newVolume = value[0];
    setWordVolume(newVolume);
    setWordPronunciationVolume(newVolume / 100);
  };

  // Handle background music volume change
  const handleBgMusicVolumeChange = (value: number[]) => {
    const newVolume = value[0];
    setBgMusicVolume(newVolume);
    setBackgroundMusicVolume(newVolume / 100);
  };

  // Handle sound effects volume change
  const handleEffectsVolumeChange = (value: number[]) => {
    const newVolume = value[0];
    setEffectsVolume(newVolume);
    setSoundEffectsVolume(newVolume / 100);
  };

  // Handle mute toggle
  const handleMuteToggle = (checked: boolean) => {
    setIsMuted(!checked);
    toast.info(checked ? "Audio enabled" : "Audio muted");
  };

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-base sm:text-lg text-amber-900">
          Audio Settings
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Background Music */}
        <div className="flex items-center justify-between p-3 rounded-md border border-amber-200">
          <div className="flex items-center gap-2">
            <Music size={16} className="text-amber-700" />
            <span className="text-sm text-amber-900">
              Enable Audio
            </span>
          </div>
          <Switch
            checked={!isMuted}
            onCheckedChange={handleMuteToggle}
            className="data-[state=checked]:bg-amber-600"
          />
        </div>

        {/* Word Pronunciation Volume */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium flex items-center gap-2 text-amber-800">
              <Volume2 size={16} className="text-amber-700" />
              Word Pronunciation Volume
            </label>
            <span className="text-xs text-amber-700">
              {wordVolume}%
            </span>
          </div>
          <Slider
            value={[wordVolume]}
            onValueChange={handleWordVolumeChange}
            max={100}
            step={1}
            className="[&>span]:bg-amber-600"
            disabled={isMuted}
          />
          <div className="flex justify-between text-xs text-amber-600">
            <span>Off</span>
            <span>Max</span>
          </div>
        </div>

        {/* Sound Effects Volume */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium flex items-center gap-2 text-amber-800">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-amber-700"
              >
                <path d="M2 9.5V4a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5.5" />
                <path d="M2 14.5V20a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-5.5" />
                <path d="M2 12h20" />
              </svg>
              Sound Effects Volume
            </label>
            <span className="text-xs text-amber-700">
              {effectsVolume}%
            </span>
          </div>
          <Slider
            value={[effectsVolume]}
            onValueChange={handleEffectsVolumeChange}
            max={100}
            step={1}
            className="[&>span]:bg-amber-600"
            disabled={isMuted}
          />
          <div className="flex justify-between text-xs text-amber-600">
            <span>Off</span>
            <span>Max</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
