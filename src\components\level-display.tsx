'use client'

import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent } from '@/components/ui/card';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Star, TrendingUp, Award } from 'lucide-react';
import { cn } from '@/lib/utils';
import { getLevelInfo, formatXP, getLevelTitle, type LevelInfo } from '@/utils/level-system';

interface LevelDisplayProps {
  totalXP: number;
  variant?: 'compact' | 'detailed' | 'card';
  showProgress?: boolean;
  showTitle?: boolean;
  className?: string;
}

export default function LevelDisplay({ 
  totalXP, 
  variant = 'compact', 
  showProgress = true,
  showTitle = false,
  className 
}: LevelDisplayProps) {
  const levelInfo = getLevelInfo(totalXP);
  const levelTitle = getLevelTitle(levelInfo.currentLevel);

  const getLevelColor = (level: number) => {
    if (level >= 50) return 'from-yellow-400 to-orange-500'; // Legendary
    if (level >= 25) return 'from-purple-400 to-pink-500'; // Epic
    if (level >= 15) return 'from-blue-400 to-cyan-500'; // Rare
    if (level >= 5) return 'from-green-400 to-emerald-500'; // Uncommon
    return 'from-gray-400 to-gray-500'; // Common
  };

  const getLevelBadgeStyle = (level: number) => {
    if (level >= 50) return 'bg-gradient-to-r from-yellow-400 to-orange-500 text-white shadow-lg';
    if (level >= 25) return 'bg-gradient-to-r from-purple-400 to-pink-500 text-white shadow-md';
    if (level >= 15) return 'bg-gradient-to-r from-blue-400 to-cyan-500 text-white shadow-md';
    if (level >= 5) return 'bg-gradient-to-r from-green-400 to-emerald-500 text-white shadow-sm';
    return 'bg-gradient-to-r from-gray-400 to-gray-500 text-white';
  };

  if (variant === 'compact') {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Badge className={cn(
              getLevelBadgeStyle(levelInfo.currentLevel),
              'cursor-help',
              className
            )}>
              <Star className="h-3 w-3 mr-1" />
              Level {levelInfo.currentLevel}
            </Badge>
          </TooltipTrigger>
          <TooltipContent>
            <div className="text-center">
              <p className="font-semibold">{levelTitle}</p>
              <p className="text-sm">{formatXP(levelInfo.currentXP)}</p>
              {showProgress && (
                <p className="text-xs text-gray-500">
                  {formatXP(levelInfo.xpToNextLevel)} to next level
                </p>
              )}
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  if (variant === 'detailed') {
    return (
      <div className={cn('space-y-2', className)}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Badge className={getLevelBadgeStyle(levelInfo.currentLevel)}>
              <Star className="h-3 w-3 mr-1" />
              Level {levelInfo.currentLevel}
            </Badge>
            {showTitle && (
              <Badge variant="outline" className="text-xs">
                {levelTitle}
              </Badge>
            )}
          </div>
          <div className="text-right text-sm">
            <p className="font-medium">{formatXP(levelInfo.currentXP)}</p>
            <p className="text-xs text-gray-500">
              {formatXP(levelInfo.xpToNextLevel)} to next level
            </p>
          </div>
        </div>
        
        {showProgress && (
          <div className="space-y-1">
            <Progress 
              value={levelInfo.progressPercentage} 
              className="h-2"
            />
            <div className="flex justify-between text-xs text-gray-500">
              <span>{formatXP(levelInfo.xpForCurrentLevel)}</span>
              <span>{levelInfo.progressPercentage}%</span>
              <span>{formatXP(levelInfo.xpForNextLevel)}</span>
            </div>
          </div>
        )}
      </div>
    );
  }

  if (variant === 'card') {
    return (
      <Card className={cn('overflow-hidden', className)}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <div className={cn(
                'p-2 rounded-full bg-gradient-to-r',
                getLevelColor(levelInfo.currentLevel)
              )}>
                <Star className="h-4 w-4 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-lg">Level {levelInfo.currentLevel}</h3>
                <p className="text-sm text-gray-600">{levelTitle}</p>
              </div>
            </div>
            <div className="text-right">
              <p className="font-bold text-lg">{formatXP(levelInfo.currentXP)}</p>
              <p className="text-xs text-gray-500">Total XP</p>
            </div>
          </div>
          
          {showProgress && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Progress to Level {levelInfo.currentLevel + 1}</span>
                <span className="font-medium">{levelInfo.progressPercentage}%</span>
              </div>
              <Progress 
                value={levelInfo.progressPercentage} 
                className="h-3"
              />
              <div className="flex justify-between text-xs text-gray-500">
                <span>{formatXP(levelInfo.xpForCurrentLevel)}</span>
                <span>{formatXP(levelInfo.xpToNextLevel)} to go</span>
                <span>{formatXP(levelInfo.xpForNextLevel)}</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  return null;
}

// XP Gain Animation Component
interface XPGainProps {
  xpGained: number;
  breakdown: string[];
  show: boolean;
  onClose: () => void;
}

export function XPGainAnimation({ xpGained, breakdown, show, onClose }: XPGainProps) {
  if (!show) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 pointer-events-none">
      <div className="bg-white rounded-lg shadow-xl border-2 border-green-200 p-6 animate-in slide-in-from-bottom-4 duration-500">
        <div className="text-center">
          <div className="flex items-center justify-center gap-2 mb-2">
            <TrendingUp className="h-6 w-6 text-green-600" />
            <h3 className="text-xl font-bold text-green-700">
              +{formatXP(xpGained)}
            </h3>
          </div>
          
          <div className="space-y-1 text-sm text-gray-600">
            {breakdown.map((line, index) => (
              <p key={index}>{line}</p>
            ))}
          </div>
          
          <button
            onClick={onClose}
            className="mt-4 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors pointer-events-auto"
          >
            Continue
          </button>
        </div>
      </div>
    </div>
  );
}

// Level Up Animation Component
interface LevelUpProps {
  newLevel: number;
  show: boolean;
  onClose: () => void;
}

export function LevelUpAnimation({ newLevel, show, onClose }: LevelUpProps) {
  if (!show) return null;

  const levelTitle = getLevelTitle(newLevel);

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 pointer-events-none bg-black/50">
      <div className="bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg shadow-2xl p-8 animate-in zoom-in-50 duration-700">
        <div className="text-center text-white">
          <div className="flex items-center justify-center gap-3 mb-4">
            <Award className="h-12 w-12 animate-bounce" />
            <div>
              <h2 className="text-3xl font-bold">LEVEL UP!</h2>
              <p className="text-xl">Level {newLevel}</p>
            </div>
            <Award className="h-12 w-12 animate-bounce" />
          </div>
          
          <p className="text-lg mb-2">You are now a</p>
          <p className="text-2xl font-bold mb-6">{levelTitle}</p>
          
          <button
            onClick={onClose}
            className="px-6 py-3 bg-white text-orange-600 rounded-lg font-semibold hover:bg-gray-100 transition-colors pointer-events-auto"
          >
            Awesome!
          </button>
        </div>
      </div>
    </div>
  );
}
