import React from "react";
import { createClient } from "../../../supabase/server";
import { redirect } from "next/navigation";
import DashboardNavbar from "@/components/dashboard-navbar";
import {
  User,
  BookOpen,
  Volume2,
  Bell,
  Shield,
  HelpCircle,
  ChevronDown,
  Upload,
  Check,
  X,
  Moon,
  Sun,
  VolumeX,
  Music,
  Globe,
  Lock,
  UserX,
  Flag,
  MessageSquare,
} from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { cn } from "@/lib/utils";
import { GameplaySettings } from "@/components/gameplay-settings";

export default async function SettingsPage() {
  const supabase = await createClient();

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return redirect("/sign-in");
  }

  // Mock data for the settings page
  const settingsData = {
    username: "WordMaster",
    title: "SpellMaster ⭐ | Undefeated",
    joinDate: "March 2024",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=WordMaster",
    linkedAccounts: {
      google: true,
      github: false,
      email: true,
    },
    audioSettings: {
      backgroundMusic: false,
      wordPronunciation: 80,
      soundEffects: 60,
    },
    visualSettings: {
      darkMode: false,
    },
    notificationSettings: {
      matchReminders: true,
      achievements: true,
      friendRequests: false,
    },
    privacySettings: {
      publicProfile: true,
    },
  };

  // Avatar options for customization
  const avatarOptions = [
    { name: "Book", seed: "Book" },
    { name: "Mug", seed: "Mug" },
    { name: "Owl", seed: "Owl" },
    { name: "Cat", seed: "Cat" },
    { name: "Fox", seed: "Fox" },
  ];

  // Available titles
  const availableTitles = [
    "Word Wanderer",
    "Lexicon Legend",
    "Spelling Sage",
    "Vocabulary Virtuoso",
    "Grammar Guardian",
  ];

  return (
    <>
      <DashboardNavbar />
      <main className="w-full bg-amber-50/30 min-h-screen">
        <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-8 flex flex-col gap-4 sm:gap-6 md:gap-8">
          {/* Header Section */}
          <header className="flex flex-col gap-2 sm:gap-4">
            <h1 className="text-2xl sm:text-3xl font-bold text-amber-900">
              Settings
            </h1>
            <div className="bg-amber-100 text-xs sm:text-sm p-2 sm:p-3 px-3 sm:px-4 rounded-lg text-amber-800 flex gap-2 items-center border border-amber-200">
              <BookOpen size={14} className="hidden xs:inline" />
              <BookOpen size={12} className="xs:hidden" />
              <span>Customize your Word Nook experience</span>
            </div>
          </header>

          {/* Settings Content */}
          <div className="grid grid-cols-1 gap-6">
            <Accordion type="single" collapsible className="w-full">
              {/* 1. Account Settings */}
              <AccordionItem
                value="account"
                className="border-b border-amber-200"
              >
                <AccordionTrigger className="py-4 text-amber-900 hover:text-amber-700 hover:no-underline">
                  <div className="flex items-center gap-2">
                    <div className="bg-amber-200 p-1.5 rounded-full">
                      <User size={18} className="text-amber-700" />
                    </div>
                    <span className="font-semibold text-base sm:text-lg">
                      Account Settings
                    </span>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="pb-4 pt-2">
                  <Card className="border-amber-200 shadow-sm bg-white">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base sm:text-lg text-amber-900">
                        Profile Customization
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {/* Username */}
                      <div className="space-y-2">
                        <label className="text-sm font-medium text-amber-800">
                          Username
                        </label>
                        <div className="flex gap-2">
                          <Input
                            defaultValue={settingsData.username}
                            className="border-amber-200 focus-visible:ring-amber-500"
                          />
                          <Button className="bg-amber-600 hover:bg-amber-700 text-white">
                            Save
                          </Button>
                        </div>
                      </div>

                      {/* Avatar Selection */}
                      <div className="space-y-2">
                        <label className="text-sm font-medium text-amber-800">
                          Avatar
                        </label>
                        <div className="flex flex-wrap gap-3 items-center">
                          <Avatar className="h-16 w-16 border-2 border-amber-300">
                            <AvatarImage
                              src={settingsData.avatar}
                              alt={settingsData.username}
                            />
                            <AvatarFallback className="bg-amber-200 text-amber-800">
                              {settingsData.username
                                .substring(0, 2)
                                .toUpperCase()}
                            </AvatarFallback>
                          </Avatar>

                          <div className="flex flex-wrap gap-2">
                            {avatarOptions.map((option, index) => (
                              <div
                                key={index}
                                className={cn(
                                  "flex flex-col items-center cursor-pointer",
                                  option.seed === "Book"
                                    ? "opacity-100"
                                    : "opacity-70 hover:opacity-100",
                                )}
                              >
                                <Avatar className="h-10 w-10 border hover:border-amber-500 transition-all">
                                  <AvatarImage
                                    src={`https://api.dicebear.com/7.x/avataaars/svg?seed=${option.seed}`}
                                    alt={option.name}
                                  />
                                  <AvatarFallback className="bg-amber-200 text-amber-800 text-xs">
                                    {option.name.substring(0, 1)}
                                  </AvatarFallback>
                                </Avatar>
                                <span className="text-[10px] mt-1 text-amber-800">
                                  {option.name}
                                </span>
                              </div>
                            ))}
                            <Button
                              variant="outline"
                              className="h-10 w-10 rounded-full border-dashed border-amber-300 flex items-center justify-center"
                            >
                              <Upload size={14} className="text-amber-700" />
                            </Button>
                          </div>
                        </div>
                      </div>

                      {/* Title Selection */}
                      <div className="space-y-2">
                        <label className="text-sm font-medium text-amber-800">
                          Title
                        </label>
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                          {availableTitles.map((title, index) => (
                            <div
                              key={index}
                              className={cn(
                                "flex items-center justify-between p-2 rounded-md border cursor-pointer",
                                index === 0
                                  ? "bg-amber-100 border-amber-300"
                                  : "border-amber-200 hover:bg-amber-50",
                              )}
                            >
                              <span className="text-sm text-amber-900">
                                {title}
                              </span>
                              {index === 0 && (
                                <Check size={16} className="text-amber-600" />
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    </CardContent>

                    <CardHeader className="pb-2 pt-4 border-t border-amber-100">
                      <CardTitle className="text-base sm:text-lg text-amber-900">
                        Linked Accounts
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex items-center justify-between p-2 rounded-md border border-amber-200 bg-amber-50">
                        <div className="flex items-center gap-2">
                          <div className="bg-white p-1 rounded-full">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              className="text-red-500"
                            >
                              <path d="M22 12a10 10 0 1 1-20 0 10 10 0 0 1 20 0Z" />
                              <path d="m15 9-6 6" />
                              <path d="m9 9 6 6" />
                            </svg>
                          </div>
                          <span className="text-sm text-amber-900">Google</span>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 text-xs hover:bg-amber-200/50"
                        >
                          {settingsData.linkedAccounts.google
                            ? "Disconnect"
                            : "Connect"}
                        </Button>
                      </div>

                      <div className="flex items-center justify-between p-2 rounded-md border border-amber-200">
                        <div className="flex items-center gap-2">
                          <div className="bg-amber-50 p-1 rounded-full">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              className="text-gray-800"
                            >
                              <path d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4" />
                              <path d="M9 18c-4.51 2-5-2-7-2" />
                            </svg>
                          </div>
                          <span className="text-sm text-amber-900">GitHub</span>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 text-xs hover:bg-amber-200/50"
                        >
                          {settingsData.linkedAccounts.github
                            ? "Disconnect"
                            : "Connect"}
                        </Button>
                      </div>

                      <div className="flex items-center justify-between p-2 rounded-md border border-amber-200 bg-amber-50">
                        <div className="flex items-center gap-2">
                          <div className="bg-white p-1 rounded-full">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              className="text-amber-700"
                            >
                              <rect width="20" height="16" x="2" y="4" rx="2" />
                              <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7" />
                            </svg>
                          </div>
                          <span className="text-sm text-amber-900">Email</span>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 text-xs hover:bg-amber-200/50"
                        >
                          {settingsData.linkedAccounts.email
                            ? "Disconnect"
                            : "Connect"}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </AccordionContent>
              </AccordionItem>

              {/* 2. Gameplay Preferences */}
              <AccordionItem
                value="gameplay"
                className="border-b border-amber-200"
              >
                <AccordionTrigger className="py-4 text-amber-900 hover:text-amber-700 hover:no-underline">
                  <div className="flex items-center gap-2">
                    <div className="bg-green-200 p-1.5 rounded-full">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="18"
                        height="18"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-green-700"
                      >
                        <path d="m6 9 6 6 6-6" />
                      </svg>
                    </div>
                    <span className="font-semibold text-base sm:text-lg">
                      Gameplay Preferences
                    </span>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="pb-4 pt-2">
                  {/* Use the client component for gameplay settings */}
                  <div className="gameplay-settings-wrapper">
                    <GameplaySettings initialDarkMode={settingsData.visualSettings.darkMode} />
                  </div>
                </AccordionContent>
              </AccordionItem>

              {/* 3. Notifications */}
              <AccordionItem
                value="notifications"
                className="border-b border-amber-200"
              >
                <AccordionTrigger className="py-4 text-amber-900 hover:text-amber-700 hover:no-underline">
                  <div className="flex items-center gap-2">
                    <div className="bg-blue-200 p-1.5 rounded-full">
                      <Bell size={18} className="text-blue-700" />
                    </div>
                    <span className="font-semibold text-base sm:text-lg">
                      Notifications
                    </span>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="pb-4 pt-2">
                  <Card className="border-amber-200 shadow-sm bg-white">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base sm:text-lg text-amber-900">
                        Dashboard Alerts
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex items-center justify-between p-3 rounded-md border border-amber-200">
                        <div>
                          <div className="flex items-center gap-2">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              className="text-amber-700"
                            >
                              <circle cx="12" cy="12" r="10" />
                              <polyline points="12 6 12 12 16 14" />
                            </svg>
                            <span className="text-sm text-amber-900">
                              Match Reminders
                            </span>
                          </div>
                          <p className="text-xs text-amber-700 mt-1 ml-6">
                            "Your match starts in 5 minutes!"
                          </p>
                        </div>
                        <Switch
                          checked={
                            settingsData.notificationSettings.matchReminders
                          }
                          className="data-[state=checked]:bg-amber-600"
                        />
                      </div>

                      <div className="flex items-center justify-between p-3 rounded-md border border-amber-200">
                        <div>
                          <div className="flex items-center gap-2">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              className="text-amber-700"
                            >
                              <path d="M12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83Z" />
                              <path d="m22 17.65-9.17 4.16a2 2 0 0 1-1.66 0L2 17.65" />
                              <path d="m22 12.65-9.17 4.16a2 2 0 0 1-1.66 0L2 12.65" />
                            </svg>
                            <span className="text-sm text-amber-900">
                              Achievements
                            </span>
                          </div>
                          <p className="text-xs text-amber-700 mt-1 ml-6">
                            "You unlocked a new badge!"
                          </p>
                        </div>
                        <Switch
                          checked={
                            settingsData.notificationSettings.achievements
                          }
                          className="data-[state=checked]:bg-amber-600"
                        />
                      </div>

                      <div className="flex items-center justify-between p-3 rounded-md border border-amber-200">
                        <div>
                          <div className="flex items-center gap-2">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              className="text-amber-700"
                            >
                              <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                              <circle cx="9" cy="7" r="4" />
                              <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
                              <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                            </svg>
                            <span className="text-sm text-amber-900">
                              Friend Requests
                            </span>
                          </div>
                          <p className="text-xs text-amber-700 mt-1 ml-6">
                            "WordWizard wants to be your friend!"
                          </p>
                        </div>
                        <Switch
                          checked={
                            settingsData.notificationSettings.friendRequests
                          }
                          className="data-[state=checked]:bg-amber-600"
                        />
                      </div>
                    </CardContent>
                  </Card>
                </AccordionContent>
              </AccordionItem>

              {/* 4. Privacy & Safety */}
              <AccordionItem
                value="privacy"
                className="border-b border-amber-200"
              >
                <AccordionTrigger className="py-4 text-amber-900 hover:text-amber-700 hover:no-underline">
                  <div className="flex items-center gap-2">
                    <div className="bg-purple-200 p-1.5 rounded-full">
                      <Shield size={18} className="text-purple-700" />
                    </div>
                    <span className="font-semibold text-base sm:text-lg">
                      Privacy & Safety
                    </span>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="pb-4 pt-2">
                  <Card className="border-amber-200 shadow-sm bg-white">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base sm:text-lg text-amber-900">
                        Visibility Controls
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-center justify-between p-3 rounded-md border border-amber-200">
                        <div className="flex items-center gap-2">
                          {settingsData.privacySettings.publicProfile ? (
                            <Globe size={16} className="text-amber-700" />
                          ) : (
                            <Lock size={16} className="text-amber-700" />
                          )}
                          <span className="text-sm text-amber-900">
                            {settingsData.privacySettings.publicProfile
                              ? "Public Profile"
                              : "Private Profile"}
                          </span>
                        </div>
                        <Switch
                          checked={settingsData.privacySettings.publicProfile}
                          className="data-[state=checked]:bg-amber-600"
                        />
                      </div>
                      <p className="text-xs text-amber-700">
                        {settingsData.privacySettings.publicProfile
                          ? "Your profile, stats, and badges are visible to everyone."
                          : "Only friends can see your detailed profile information."}
                      </p>
                    </CardContent>

                    <CardHeader className="pb-2 pt-4 border-t border-amber-100">
                      <CardTitle className="text-base sm:text-lg text-amber-900">
                        Block & Report
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex items-center justify-between p-3 rounded-md border border-amber-200">
                        <div className="flex items-center gap-2">
                          <UserX size={16} className="text-amber-700" />
                          <span className="text-sm text-amber-900">
                            Blocked Players
                          </span>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8 text-xs border-amber-300 hover:bg-amber-100"
                        >
                          Manage
                        </Button>
                      </div>

                      <div className="flex items-center justify-between p-3 rounded-md border border-amber-200">
                        <div className="flex items-center gap-2">
                          <Flag size={16} className="text-amber-700" />
                          <span className="text-sm text-amber-900">
                            Report a Player
                          </span>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8 text-xs border-amber-300 hover:bg-amber-100"
                        >
                          Report
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </AccordionContent>
              </AccordionItem>

              {/* 5. Data & Support */}
              <AccordionItem
                value="support"
                className="border-b border-amber-200"
              >
                <AccordionTrigger className="py-4 text-amber-900 hover:text-amber-700 hover:no-underline">
                  <div className="flex items-center gap-2">
                    <div className="bg-red-200 p-1.5 rounded-full">
                      <HelpCircle size={18} className="text-red-700" />
                    </div>
                    <span className="font-semibold text-base sm:text-lg">
                      Help & Support
                    </span>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="pb-4 pt-2">
                  <Card className="border-amber-200 shadow-sm bg-white">
                    <CardContent className="pt-4 space-y-3">
                      <div className="flex items-center justify-between p-3 rounded-md border border-amber-200 bg-amber-50">
                        <div className="flex items-center gap-2">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="16"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="text-amber-700"
                          >
                            <circle cx="12" cy="12" r="10" />
                            <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3" />
                            <path d="M12 17h.01" />
                          </svg>
                          <span className="text-sm text-amber-900">
                            Frequently Asked Questions
                          </span>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 text-xs hover:bg-amber-200/50"
                        >
                          View FAQ
                        </Button>
                      </div>

                      <div className="flex items-center justify-between p-3 rounded-md border border-amber-200">
                        <div className="flex items-center gap-2">
                          <MessageSquare size={16} className="text-amber-700" />
                          <span className="text-sm text-amber-900">
                            Contact Support
                          </span>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 text-xs hover:bg-amber-200/50"
                        >
                          Send Message
                        </Button>
                      </div>

                      <div className="mt-4 p-3 rounded-md bg-amber-100/50 border border-amber-200">
                        <h4 className="text-sm font-medium text-amber-900 mb-1">
                          About Word Nook
                        </h4>
                        <p className="text-xs text-amber-700">Version 1.0.0</p>
                        <div className="flex gap-2 mt-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-7 text-xs border-amber-300 hover:bg-amber-100"
                          >
                            Terms of Service
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-7 text-xs border-amber-300 hover:bg-amber-100"
                          >
                            Privacy Policy
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </AccordionContent>
              </AccordionItem>
            </Accordion>

            <div className="flex justify-end mt-4">
              <Button className="bg-amber-600 hover:bg-amber-700 text-white">
                Save All Changes
              </Button>
            </div>
          </div>
        </div>
      </main>
    </>
  );
}
