'use client';

import { But<PERSON> } from "@/components/ui/button";
import { signInWithGoogleAction } from "@/app/actions";
import Image from "next/image";

export function GoogleSignInButton() {
  return (
    <Button
      variant="outline"
      className="w-full flex items-center justify-center gap-3 py-5 border-stone-200 hover:bg-stone-50 hover:border-stone-300 transition-all duration-200"
      onClick={async () => {
        await signInWithGoogleAction();
      }}
    >
      <Image
        src="/google.svg"
        alt="Google logo"
        width={20}
        height={20}
        className="transition-transform group-hover:scale-110"
      />
      <span className="font-medium text-stone-700">Continue with Google</span>
    </Button>
  );
}
