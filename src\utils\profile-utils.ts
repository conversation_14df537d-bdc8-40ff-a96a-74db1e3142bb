import { createClient } from "../../supabase/client";

export interface MatchHistoryEntry {
  match_id: string;
  difficulty: string;
  final_rank: number;
  score: number;
  elimination_round: number | null;
  ended_at: string;
  current_round: number;
  match_duration_seconds: number;
  total_players: number;
  player_names: string[];
}

export interface PlayerStats {
  total_matches: number;
  wins: number;
  win_rate: number;
  highest_score: number;
  longest_streak: number;
  total_score: number;
  avg_score: number;
  favorite_difficulty: string;
  total_eliminations: number;
  survival_rate: number;
}

export interface AvatarOption {
  id: string;
  name: string;
  url: string;
  locked: boolean;
  unlock_condition?: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
}

/**
 * Get user's match history with detailed information
 */
export async function getUserMatchHistory(userId: string, limit: number = 20): Promise<MatchHistoryEntry[]> {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('match_history_players')
      .select(`
        match_id,
        score,
        final_rank,
        elimination_round,
        difficulty,
        created_at,
        match_histories (
          current_round,
          match_duration_seconds,
          ended_at,
          player_ids
        )
      `)
      .eq('player_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching match history:', error);
      return [];
    }

    if (!data || data.length === 0) {
      return [];
    }

    // Get player names for each match
    const matchHistoryWithNames = await Promise.all(
      data.map(async (match: any) => {
        const playerIds = match.match_histories?.player_ids || [];
        
        // Get player names
        const { data: playersData } = await supabase
          .from('players')
          .select('id, display_name')
          .in('id', playerIds);

        const playerNames = playersData?.map(p => p.display_name) || [];

        return {
          match_id: match.match_id,
          difficulty: match.difficulty,
          final_rank: match.final_rank || 0,
          score: match.score || 0,
          elimination_round: match.elimination_round,
          ended_at: match.match_histories?.ended_at || match.created_at,
          current_round: match.match_histories?.current_round || 1,
          match_duration_seconds: match.match_histories?.match_duration_seconds || 0,
          total_players: playerIds.length,
          player_names: playerNames
        };
      })
    );

    return matchHistoryWithNames;

  } catch (error) {
    console.error('Exception in getUserMatchHistory:', error);
    return [];
  }
}

/**
 * Get comprehensive player statistics
 */
export async function getPlayerStats(userId: string): Promise<PlayerStats> {
  const supabase = createClient();

  try {
    // Get match history data
    const { data: matchData, error } = await supabase
      .from('match_history_players')
      .select('score, final_rank, difficulty, elimination_round')
      .eq('player_id', userId);

    if (error || !matchData) {
      return {
        total_matches: 0,
        wins: 0,
        win_rate: 0,
        highest_score: 0,
        longest_streak: 0,
        total_score: 0,
        avg_score: 0,
        favorite_difficulty: 'easy',
        total_eliminations: 0,
        survival_rate: 0
      };
    }

    const totalMatches = matchData.length;
    const wins = matchData.filter(m => m.final_rank === 1).length;
    const scores = matchData.map(m => m.score || 0);
    const totalScore = scores.reduce((a, b) => a + b, 0);
    const eliminations = matchData.filter(m => m.elimination_round && m.elimination_round > 0).length;

    // Calculate favorite difficulty
    const difficultyCount = matchData.reduce((acc: any, match) => {
      acc[match.difficulty] = (acc[match.difficulty] || 0) + 1;
      return acc;
    }, {});
    const favoriteDifficulty = Object.keys(difficultyCount).reduce((a, b) => 
      difficultyCount[a] > difficultyCount[b] ? a : b, 'easy'
    );

    // Get longest streak from user_stats
    const { data: userStats } = await supabase
      .from('user_stats')
      .select('longest_streak')
      .eq('user_id', userId)
      .order('longest_streak', { ascending: false })
      .limit(1)
      .single();

    return {
      total_matches: totalMatches,
      wins,
      win_rate: totalMatches > 0 ? Math.round((wins / totalMatches) * 100) : 0,
      highest_score: Math.max(...scores, 0),
      longest_streak: userStats?.longest_streak || 0,
      total_score: totalScore,
      avg_score: totalMatches > 0 ? Math.round(totalScore / totalMatches) : 0,
      favorite_difficulty: favoriteDifficulty,
      total_eliminations: eliminations,
      survival_rate: totalMatches > 0 ? Math.round(((totalMatches - eliminations) / totalMatches) * 100) : 0
    };

  } catch (error) {
    console.error('Exception in getPlayerStats:', error);
    return {
      total_matches: 0,
      wins: 0,
      win_rate: 0,
      highest_score: 0,
      longest_streak: 0,
      total_score: 0,
      avg_score: 0,
      favorite_difficulty: 'easy',
      total_eliminations: 0,
      survival_rate: 0
    };
  }
}

/**
 * Get available avatar options with unlock status
 * Using DiceBear API for consistent, themed avatars
 */
export function getAvatarOptions(userLevel: number = 1): AvatarOption[] {
  const avatars: AvatarOption[] = [
    // Common avatars (always unlocked)
    { id: 'default', name: 'Default', url: '', locked: false, rarity: 'common' },
    { id: 'scholar', name: 'Scholar', url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Scholar&backgroundColor=f3f4f6', locked: false, rarity: 'common' },
    { id: 'bookworm', name: 'Bookworm', url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Bookworm&backgroundColor=fef3c7', locked: false, rarity: 'common' },
    { id: 'student', name: 'Student', url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Student&backgroundColor=dbeafe', locked: false, rarity: 'common' },

    // Rare avatars (unlock at level 5)
    { id: 'wizard', name: 'Word Wizard', url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Wizard&backgroundColor=e0e7ff', locked: userLevel < 5, unlock_condition: 'Reach Level 5', rarity: 'rare' },
    { id: 'sage', name: 'Spelling Sage', url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Sage&backgroundColor=ddd6fe', locked: userLevel < 5, unlock_condition: 'Reach Level 5', rarity: 'rare' },
    { id: 'professor', name: 'Professor', url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Professor&backgroundColor=f0f9ff', locked: userLevel < 8, unlock_condition: 'Reach Level 8', rarity: 'rare' },

    // Epic avatars (unlock with achievements)
    { id: 'champion', name: 'Champion', url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Champion&backgroundColor=fdf4ff', locked: userLevel < 15, unlock_condition: 'Win 10 matches', rarity: 'epic' },
    { id: 'master', name: 'Spell Master', url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=SpellMaster&backgroundColor=f3e8ff', locked: userLevel < 20, unlock_condition: 'Reach Level 20', rarity: 'epic' },
    { id: 'oracle', name: 'Word Oracle', url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Oracle&backgroundColor=ecfdf5', locked: userLevel < 25, unlock_condition: 'Perfect streak of 15', rarity: 'epic' },

    // Legendary avatars (special achievements)
    { id: 'legend', name: 'Legend', url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Legend&backgroundColor=fffbeb', locked: userLevel < 50, unlock_condition: 'Reach Level 50', rarity: 'legendary' },
    { id: 'grandmaster', name: 'Grandmaster', url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Grandmaster&backgroundColor=fef7cd', locked: userLevel < 100, unlock_condition: 'Win 100 matches', rarity: 'legendary' }
  ];

  return avatars;
}

/**
 * Update user's selected avatar
 */
export async function updateUserAvatar(userId: string, avatarId: string): Promise<boolean> {
  const supabase = createClient();

  try {
    const { error } = await supabase
      .from('players')
      .update({ avatar_url: avatarId })
      .eq('id', userId);

    if (error) {
      console.error('Error updating avatar:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Exception in updateUserAvatar:', error);
    return false;
  }
}

/**
 * Format match duration for display
 */
export function formatMatchDuration(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  
  if (minutes > 0) {
    return `${minutes}m ${remainingSeconds}s`;
  }
  return `${remainingSeconds}s`;
}

/**
 * Get rank display with emoji
 */
export function getRankDisplay(rank: number): string {
  switch (rank) {
    case 1: return '🥇 1st';
    case 2: return '🥈 2nd';
    case 3: return '🥉 3rd';
    default: return `#${rank}`;
  }
}

/**
 * Get difficulty color class
 */
export function getDifficultyColor(difficulty: string): string {
  switch (difficulty.toLowerCase()) {
    case 'easy': return 'text-green-600 bg-green-100';
    case 'medium': return 'text-blue-600 bg-blue-100';
    case 'hard': return 'text-orange-600 bg-orange-100';
    case 'extreme': return 'text-purple-600 bg-purple-100';
    default: return 'text-gray-600 bg-gray-100';
  }
}
