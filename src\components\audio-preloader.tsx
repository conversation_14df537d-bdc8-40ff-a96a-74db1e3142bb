"use client";

import { useState, useEffect } from "react";
import { usePreloadAudio, getWordsForDifficulty } from "@/utils/audio-utils";
import { Progress } from "@/components/ui/progress";
import { Volume2, CheckCircle2, AlertCircle } from "lucide-react";
import { toast } from "sonner";
import { Difficulty } from "@/interfaces/interfaces";

interface AudioPreloaderProps {
  difficulty: Difficulty;
  onComplete?: () => void;
  className?: string;
}

export function AudioPreloader({
  difficulty,
  onComplete,
  className = ""
}: AudioPreloaderProps) {
  // Convert difficulty to the correct type
  const difficultyLevel = difficulty.toLowerCase() as Difficulty;

  // Get all words for this difficulty
  const wordList = getWordsForDifficulty(difficultyLevel);

  // Use our preload hook
  const { isLoaded, progress } = usePreloadAudio(difficultyLevel, wordList);

  // Track if there was an error
  const [hasError, setHasError] = useState(false);

  // Call onComplete when loading is finished
  useEffect(() => {
    if (isLoaded && onComplete) {
      onComplete();
      // toast.success(`Audio files for ${difficulty} difficulty loaded successfully!`);
    }
  }, [isLoaded, difficulty, onComplete]);

  // Handle errors
  useEffect(() => {
    const handleError = () => {
      if (progress > 0 && progress < 100 && !isLoaded) {
        const timeoutId = setTimeout(() => {
          if (progress < 100 && !isLoaded) {
            setHasError(true);
            // toast.error(`Some audio files failed to load.`);
            // Still call onComplete even if there was an error
            if (onComplete) onComplete();
          }
        }, 10000); // Wait 10 seconds before showing error

        return () => clearTimeout(timeoutId);
      }
    };

    handleError();
  }, [progress, isLoaded, onComplete]);

  return (
    <div className={`audio-preloader ${className}`}>
      <div className="flex items-center gap-2 mb-2">
        <Volume2 size={16} className="text-amber-600" />
        <span className="text-sm font-medium text-amber-800">
          Loading audio files ({progress}%)
        </span>
        {isLoaded && (
          <CheckCircle2 size={16} className="text-green-500 ml-auto" />
        )}
        {hasError && (
          <AlertCircle size={16} className="text-amber-500 ml-auto" />
        )}
      </div>

      <Progress
        value={progress}
        className="h-2 bg-amber-100"
        indicatorClassName={isLoaded ? "bg-green-500" : hasError ? "bg-amber-500" : "bg-amber-600"}
      />

      {hasError && (
        <p className="text-xs text-amber-600 mt-1">
          Some audio files couldn't be loaded.
        </p>
      )}
    </div>
  );
}
