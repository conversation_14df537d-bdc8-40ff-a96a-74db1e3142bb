import DashboardNavbar from "@/components/dashboard-navbar";
import { InfoIcon } from "lucide-react";
import { redirect } from "next/navigation";
import { createClient } from "../../../supabase/server";
import { SupabaseClient } from "@supabase/supabase-js";
import RoomCards from "@/components/room-cards";
import { Difficulty } from "@/interfaces/interfaces";

// Define types for room counts
type RoomCounts = {
  easy: number;
  medium: number;
  hard: number;
  "extreme": number;
};

// Define type for match data
type MatchData = {
  id: string;
  difficulty: Difficulty | null;
  match_players: { player_id: string }[];
};

// Define type for user match data
type UserMatchData = {
  id: string;
  difficulty: Difficulty;
  status: string;
};

// Function to check if user is in an active match
async function checkUserInActiveMatch(supabase: SupabaseClient, userId: string): Promise<UserMatchData | null> {
  try {
    // Query to find active matches that the user is part of
    const { data, error } = await supabase
      .from('matches')
      .select(`
        id,
        difficulty,
        status,
        match_players!inner(player_id)
      `)
      .in('status', ['waiting', 'ongoing'])
      .eq('match_players.player_id', userId);

    if (error) {
      console.error('Error checking if user is in active match:', error);
      return null;
    }

    if (!data || data.length === 0) {
      return null;
    }

    // Return the first active match
    const match = data[0];
    return {
      id: match.id,
      difficulty: match.difficulty,
      status: match.status
    };
  } catch (error) {
    console.error('Exception checking if user is in active match:', error);
    return null;
  }
}

// Function to get player counts for each room
async function getRoomPlayerCounts(supabase: SupabaseClient): Promise<RoomCounts> {
  // Query to count players in each difficulty level (room)
  const { data, error } = await supabase
    .from('matches')
    .select(`
      id,
      difficulty,
      match_players(player_id)
    `)
    .in('status', ['waiting', 'ongoing']);

  if (error) {
    console.error('Error fetching room counts:', error);
    return {
      easy: 0,
      medium: 0,
      hard: 0,
      "extreme": 0
    };
  }

  // Process the data to count players per difficulty level
  const roomCounts: RoomCounts = {
    easy: 0,
    medium: 0,
    hard: 0,
    "extreme": 0
  };

  (data as MatchData[]).forEach(match => {
    if (!match.difficulty) return;

    const difficulty = match.difficulty.toLowerCase();
    if (difficulty in roomCounts) {
      roomCounts[difficulty as keyof RoomCounts] += match.match_players.length;
    }
  });

  return roomCounts;
}

export default async function Dashboard() {
  const supabase = await createClient();

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return redirect("/sign-in");
  }

  // Check if user is in an active match
  const activeMatch = await checkUserInActiveMatch(supabase, user.id);

  // If user is in an active match, redirect them to the match page
  if (activeMatch) {
    const difficulty = activeMatch.difficulty.toLowerCase().replace(/\s+/g, "-");
    return redirect(`/battle/${difficulty}`);
  }

  // Get real player counts from the database
  const roomCounts = await getRoomPlayerCounts(supabase);

  return (
    <>
      <DashboardNavbar />
      <main className="w-full bg-amber-50/30">
        <div className="container mx-auto px-4 py-8 flex flex-col gap-8">
          {/* Header Section */}
          <header className="flex flex-col gap-4">
            <h1 className="text-3xl font-bold text-amber-900">
              Word Nook Lobby
            </h1>
            <div className="bg-amber-100 text-sm p-3 px-4 rounded-lg text-amber-800 flex gap-2 items-center border border-amber-200">
              <InfoIcon size="14" />
              <span>Select a difficulty room to join a spelling battle</span>
            </div>
          </header>

          {/* Difficulty Rooms Section */}
          <RoomCards initialRoomCounts={roomCounts} />
        </div>
      </main>
    </>
  );
}
