'use client'

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON>Header, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Edit, Save, X, Palette, Crown, Sparkles, Lock, Check } from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { getBackgroundOptions, updateUserProfile, type BackgroundOption } from '@/utils/profile-utils';

interface ProfileEditPopupProps {
  children: React.ReactNode;
  currentDisplayName: string;
  currentTitle: string;
  currentBackground: string;
  userLevel: number;
  userId: string;
  onProfileUpdate?: (updates: any) => void;
}

export default function ProfileEditPopup({ 
  children, 
  currentDisplayName, 
  currentTitle, 
  currentBackground,
  userLevel, 
  userId, 
  onProfileUpdate 
}: ProfileEditPopupProps) {
  const [open, setOpen] = useState(false);
  const [displayName, setDisplayName] = useState(currentDisplayName);
  const [title, setTitle] = useState(currentTitle);
  const [selectedBackground, setSelectedBackground] = useState(currentBackground);
  const [isUpdating, setIsUpdating] = useState(false);

  const backgroundOptions = getBackgroundOptions(userLevel);

  const handleSave = async () => {
    if (!displayName.trim()) {
      toast.error('Display name cannot be empty');
      return;
    }

    setIsUpdating(true);
    try {
      const updates = {
        display_name: displayName.trim(),
        title: title.trim(),
        background_display: selectedBackground
      };

      const success = await updateUserProfile(userId, updates);
      
      if (success) {
        onProfileUpdate?.(updates);
        toast.success('Profile updated successfully!');
        setOpen(false);
        
        // Refresh the page to update all components
        window.location.reload();
      } else {
        toast.error('Failed to update profile. Please try again.');
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('An error occurred while updating your profile.');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleBackgroundSelect = (background: BackgroundOption) => {
    if (background.locked) {
      toast.error(`Background locked! ${background.unlock_condition}`);
      return;
    }
    setSelectedBackground(background.url);
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'border-gray-300 bg-gray-50';
      case 'rare': return 'border-blue-300 bg-blue-50';
      case 'epic': return 'border-purple-300 bg-purple-50';
      case 'legendary': return 'border-yellow-300 bg-yellow-50';
      default: return 'border-gray-300 bg-gray-50';
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-amber-900">
            <Edit className="h-5 w-5 text-amber-600" />
            Edit Profile
          </DialogTitle>
        </DialogHeader>
        
        <Tabs defaultValue="basic" className="w-full">
          <TabsList className="grid grid-cols-2 mb-6">
            <TabsTrigger value="basic" className="flex items-center gap-2">
              <Edit className="h-4 w-4" />
              Basic Info
            </TabsTrigger>
            <TabsTrigger value="background" className="flex items-center gap-2">
              <Palette className="h-4 w-4" />
              Background
            </TabsTrigger>
          </TabsList>

          {/* Basic Info Tab */}
          <TabsContent value="basic" className="space-y-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="displayName" className="text-sm font-medium text-gray-700">
                  Display Name
                </Label>
                <Input
                  id="displayName"
                  value={displayName}
                  onChange={(e) => setDisplayName(e.target.value)}
                  placeholder="Enter your display name"
                  className="mt-1"
                  maxLength={30}
                />
                <p className="text-xs text-gray-500 mt-1">
                  {displayName.length}/30 characters
                </p>
              </div>

              <div>
                <Label htmlFor="title" className="text-sm font-medium text-gray-700">
                  Title
                </Label>
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="Enter your title (e.g., Word Master, Spelling Champion)"
                  className="mt-1"
                  maxLength={50}
                />
                <p className="text-xs text-gray-500 mt-1">
                  {title.length}/50 characters
                </p>
              </div>

              {/* Preview */}
              <div className="mt-6">
                <Label className="text-sm font-medium text-gray-700 mb-3 block">
                  Preview
                </Label>
                <div className={cn(
                  "p-6 rounded-lg border-2 text-center",
                  selectedBackground || 'bg-gradient-to-r from-amber-100 to-amber-50'
                )}>
                  <div className="space-y-2">
                    <h3 className="text-xl font-bold text-amber-900">
                      {displayName || 'Your Name'}
                    </h3>
                    <p className="text-amber-700 font-medium">
                      {title || 'Your Title'}
                    </p>
                    <Badge className="bg-amber-200 text-amber-800">
                      Level {userLevel}
                    </Badge>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Background Tab */}
          <TabsContent value="background" className="space-y-6">
            <div>
              <div className="flex items-center justify-between mb-4">
                <Label className="text-sm font-medium text-gray-700">
                  Choose Background
                </Label>
                <Badge className="bg-amber-100 text-amber-800">
                  Level {userLevel}
                </Badge>
              </div>
              
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
                {backgroundOptions.map((background) => {
                  const isSelected = selectedBackground === background.url;
                  
                  return (
                    <div
                      key={background.id}
                      className={cn(
                        "relative cursor-pointer rounded-lg border-2 transition-all duration-200",
                        background.locked 
                          ? "opacity-50 cursor-not-allowed" 
                          : "hover:scale-105",
                        isSelected && "ring-2 ring-amber-400",
                        getRarityColor(background.rarity)
                      )}
                      onClick={() => handleBackgroundSelect(background)}
                    >
                      {/* Background Preview */}
                      <div 
                        className={cn(
                          "h-24 rounded-t-lg relative overflow-hidden",
                          background.url
                        )}
                        style={{ 
                          background: background.preview 
                        }}
                      >
                        {background.locked && (
                          <div className="absolute inset-0 flex items-center justify-center bg-black/50">
                            <Lock size={20} className="text-white" />
                          </div>
                        )}
                        
                        {isSelected && !background.locked && (
                          <div className="absolute top-2 right-2 bg-amber-500 rounded-full p-1">
                            <Check size={12} className="text-white" />
                          </div>
                        )}
                        
                        {/* Rarity indicator */}
                        <div className={cn(
                          "absolute top-2 left-2 px-2 py-1 rounded text-xs font-bold",
                          background.rarity === 'common' && "bg-gray-200 text-gray-700",
                          background.rarity === 'rare' && "bg-blue-200 text-blue-700",
                          background.rarity === 'epic' && "bg-purple-200 text-purple-700",
                          background.rarity === 'legendary' && "bg-yellow-200 text-yellow-700"
                        )}>
                          {background.rarity.charAt(0).toUpperCase()}
                        </div>
                      </div>
                      
                      {/* Background Info */}
                      <div className="p-3">
                        <h4 className="font-medium text-sm text-gray-900 mb-1">
                          {background.name}
                        </h4>
                        {background.locked && background.unlock_condition && (
                          <p className="text-xs text-gray-500">
                            {background.unlock_condition}
                          </p>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
              
              <div className="mt-4 p-3 bg-amber-50 rounded-lg border border-amber-200">
                <div className="flex items-center gap-2 mb-2">
                  <Sparkles className="h-4 w-4 text-amber-600" />
                  <span className="text-sm font-medium text-amber-900">Background Rarity Guide</span>
                </div>
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div className="flex items-center gap-1">
                    <div className="w-3 h-3 bg-gray-300 rounded"></div>
                    <span>Common - Always available</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <div className="w-3 h-3 bg-blue-300 rounded"></div>
                    <span>Rare - Level rewards</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <div className="w-3 h-3 bg-purple-300 rounded"></div>
                    <span>Epic - Achievements</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <div className="w-3 h-3 bg-yellow-300 rounded"></div>
                    <span>Legendary - Special</span>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        {/* Action Buttons */}
        <div className="flex justify-end gap-3 pt-4 border-t">
          <Button 
            variant="outline" 
            onClick={() => setOpen(false)}
            disabled={isUpdating}
          >
            <X className="h-4 w-4 mr-2" />
            Cancel
          </Button>
          <Button 
            onClick={handleSave}
            disabled={isUpdating}
            className="bg-amber-600 hover:bg-amber-700"
          >
            {isUpdating ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            Save Changes
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
