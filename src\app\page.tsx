import Footer from "@/components/footer";
import Hero from "@/components/hero";
import Navbar from "@/components/navbar";
import {
  ArrowUpRight,
  BookOpen,
  Clock,
  Sparkles,
  <PERSON>,
  Gamepad2,
  Brain,
  Award,
  Users,
} from "lucide-react";
import { createClient } from "../../supabase/server";
import Image from "next/image";

export default async function Home() {
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  return (
    <div className="min-h-screen bg-[#fff8e8]">
      <Navbar />
      <Hero />

      {/* Difficulty Rooms Section */}
      <svg viewBox="0 0 1440 100" fill="none" xmlns="http://www.w3.org/2000/svg" className="bg-[white]">
          <path
            d="M0 43.9999C106.667 43.9999 213.333 7.99994 320 7.99994C426.667 7.99994 533.333 43.9999 640 43.9999C746.667 43.9999 853.333 7.99994 960 7.99994C1066.67 7.99994 1173.33 43.9999 1280 43.9999C1386.67 43.9999 1440 19.0266 1440 9.01329V100H0V43.9999Z"
            className="fill-current text-[#fff8e8] ">
          </path>
      </svg>
      <section id="difficulty-rooms" className="py-24 bg-[#fff8e8]">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold mb-4">Choose Your Difficulty</h2>
            <p className="text-gray-700 max-w-2xl mx-auto">
              Our bookshelf-style difficulty rooms offer challenges for spellers
              of all levels. Select your room and prepare for battle!
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: <BookOpen className="w-6 h-6" />,
                title: "Easy Mode",
                description: "Perfect for beginners with common everyday words",
                color: "bg-stone-100",
                iconColor: "text-stone-700",
              },
              {
                icon: <Brain className="w-6 h-6" />,
                title: "Medium Mode",
                description: "Challenge yourself with intermediate vocabulary",
                color: "bg-rose-100",
                iconColor: "text-stone-700",
              },
              {
                icon: <Award className="w-6 h-6" />,
                title: "Hard Mode",
                description: "Advanced words for seasoned spellers",
                color: "bg-yellow-100",
                iconColor: "text-stone-700",
              },
              {
                icon: <Trophy className="w-6 h-6" />,
                title: "Expert",
                description: "Expert-level challenges with rare words",
                color: "bg-red-100",
                iconColor: "text-stone-700",
              },
            ].map((room, index) => (
              <div
                key={index}
                className={`p-6 ${room.color} rounded-xl shadow-sm hover:shadow-md transition-shadow border border-stone-300`}
              >
                <div className={`${room.iconColor} mb-4`}>{room.icon}</div>
                <h3 className="text-xl font-semibold mb-2">{room.title}</h3>
                <p className="text-gray-700">{room.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Game Features Section */}
      <section className="py-24 bg-[#fff8e8]">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold mb-4">
              Spelling Battle Features
            </h2>
            <p className="text-gray-700 max-w-2xl mx-auto">
              Immerse yourself in our cozy, bookish world of competitive
              spelling
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-12">
            <div className="bg-stone-100 p-8 rounded-xl shadow-md border border-stone-300">
              <h3 className="text-2xl font-bold mb-4 text-amber-800">
                Battle System
              </h3>
              <ul className="space-y-4">
                <li className="flex items-start">
                  <Clock className="w-5 h-5 text-stone-700 mt-1 mr-3" />
                  <span>15-second typing rounds with voice prompts</span>
                </li>
                <li className="flex items-start">
                  <Users className="w-5 h-5 text-stone-700 mt-1 mr-3" />
                  <span>Real-time multiplayer with up to 8 players</span>
                </li>
                <li className="flex items-start">
                  <Sparkles className="w-5 h-5 text-stone-700 mt-1 mr-3" />
                  <span>
                    Collect power-ups like Time Freeze and Double Points
                  </span>
                </li>
                <li className="flex items-start">
                  <Gamepad2 className="w-5 h-5 text-stone-700 mt-1 mr-3" />
                  <span>Spectator mode with voting and betting features</span>
                </li>
              </ul>
            </div>

            <div className="bg-stone-100 p-8 rounded-xl shadow-md border border-stone-300">
              <h3 className="text-2xl font-bold mb-4 text-amber-800">
                Progression System
              </h3>
              <ul className="space-y-4">
                <li className="flex items-start">
                  <Trophy className="w-5 h-5 text-stone-700 mt-1 mr-3" />
                  <span>Earn badges and titles as you improve</span>
                </li>
                <li className="flex items-start">
                  <Award className="w-5 h-5 text-stone-700 mt-1 mr-3" />
                  <span>Track your stats and progress over time</span>
                </li>
                <li className="flex items-start">
                  <Brain className="w-5 h-5 text-stone-700 mt-1 mr-3" />
                  <span>Unlock new difficulty levels as you master words</span>
                </li>
                <li className="flex items-start">
                  <BookOpen className="w-5 h-5 text-stone-700 mt-1 mr-3" />
                  <span>Build your personal word collection</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-stone-800 text-white">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-3 gap-8 text-center">
            <div>
              <div className="text-4xl font-bold mb-2">10,000+</div>
              <div className="text-stone-300">Words in Database</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">4</div>
              <div className="text-stone-300">Difficulty Levels</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">8</div>
              <div className="text-stone-300">Power-Up Types</div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-[#fff8e8]">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">
            Ready to Test Your Spelling?
          </h2>
          <p className="text-gray-700 mb-8 max-w-2xl mx-auto">
            Join our cozy community of word enthusiasts and battle your way to
            the top of the leaderboard!
          </p>
          <a
            href="/dashboard"
            className="inline-flex items-center px-6 py-3 text-white bg-[#8B4513] rounded-lg hover:bg-[#A0522D] transition-colors shadow-md border border-[#A0522D]"
          >
            Enter Word Nook
            <ArrowUpRight className="ml-2 w-4 h-4" />
          </a>
        </div>
      </section>

      <Footer />
    </div>
  );
}
