import Link from "next/link";
import {
  ArrowUpR<PERSON>,
  Check,
  BookOpen,
  Trophy,
  Users,
  Sparkles,
} from "lucide-react";
import Image from "next/image";

export default function Hero() {
  return (
    <div className="relative overflow-hidden">
      {/* Background gradient - warm, bookish feel */}
      <div className="absolute inset-0 bg-[white]" />

      <div className="relative pt-24 pb-32 sm:pt-32 sm:pb-40">
        <div className="container mx-auto px-4">
          <div className="flex flex-col lg:ml-20 lg:flex-row items-center gap-12">
            <div className="text-center lg:text-left max-w-2xl lg:max-w-xl mx-auto lg:mx-0">
              <h1 className="text-5xl sm:text-6xl font-bold text-gray-900 mb-8 tracking-tight">
                Word{" "}
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-amber-600 to-red-600">
                  Nook
                </span>{" "}
                Spelling Battle
              </h1>

              <p className="text-xl text-gray-700 mb-12 leading-relaxed">
                A cozy multiplayer spelling game where words come alive! Challenge
                friends across difficulty tiers, collect power-ups, and climb the
                leaderboard in our warm, bookish world.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start items-center">
                <Link
                  href="/dashboard"
                  className="inline-flex items-center px-8 py-4 text-white bg-[#8B4513] rounded-lg hover:bg-[#A0522D] transition-colors text-lg font-medium shadow-md border border-[#A0522D]"
                >
                  Enter the Battle
                  <ArrowUpRight className="ml-2 w-5 h-5" />
                </Link>

                <Link
                  href="#difficulty-rooms"
                  className="inline-flex items-center px-8 py-4 text-gray-800 bg-[#DEB887] rounded-lg hover:bg-[#D2B48C] transition-colors text-lg font-medium shadow-sm border border-[#A0522D]"
                >
                  View Difficulty Rooms
                </Link>
              </div>

              <div className="mt-16 flex flex-col sm:flex-row items-center justify-center lg:justify-start gap-8 text-sm text-gray-700">
                <div className="flex items-center gap-2">
                  <BookOpen className="w-5 h-5 text-amber-500" />
                  <span>Cozy bookish aesthetic</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="w-5 h-5 text-amber-500" />
                  <span>Real-time multiplayer</span>
                </div>
                <div className="flex items-center gap-2">
                  <Trophy className="w-5 h-5 text-amber-500" />
                  <span>Earn badges & titles</span>
                </div>
                <div className="flex items-center gap-2">
                  <Sparkles className="w-5 h-5 text-amber-500" />
                  <span>Collect power-ups</span>
                </div>
              </div>
            </div>
            <div className="lg:mt-0 lg:w-1/2 lg:ml-20 flex justify-center hidden lg:block">
              <Image
                src="/logo.png"
                alt="Hero Image"
                width={500}
                height={500}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
