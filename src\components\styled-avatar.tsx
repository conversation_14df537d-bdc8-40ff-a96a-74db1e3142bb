'use client'

import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { cn } from '@/lib/utils';

interface StyledAvatarProps {
  src?: string | null;
  alt?: string;
  fallback?: string;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  border?: string | null;
  className?: string;
}

export default function StyledAvatar({ 
  src, 
  alt, 
  fallback, 
  size = 'md', 
  border,
  className 
}: StyledAvatarProps) {
  const sizeClasses = {
    xs: 'h-6 w-6',
    sm: 'h-8 w-8',
    md: 'h-10 w-10',
    lg: 'h-12 w-12',
    xl: 'h-16 w-16',
    '2xl': 'h-20 w-20'
  };

  const fallbackSizeClasses = {
    xs: 'text-xs',
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
    xl: 'text-lg',
    '2xl': 'text-xl'
  };

  const defaultBorder = 'border-2 border-white';
  const appliedBorder = border || defaultBorder;
  const hasGradientBorder = appliedBorder.includes('bg-gradient');

  if (hasGradientBorder) {
    return (
      <div className={cn(
        "p-1 rounded-full",
        appliedBorder,
        className
      )}>
        <Avatar className={cn(sizeClasses[size])}>
          <AvatarImage src={src || ''} alt={alt} />
          <AvatarFallback className={cn(
            "bg-amber-200 text-amber-800",
            fallbackSizeClasses[size]
          )}>
            {fallback}
          </AvatarFallback>
        </Avatar>
      </div>
    );
  }

  return (
    <Avatar className={cn(
      sizeClasses[size],
      appliedBorder,
      className
    )}>
      <AvatarImage src={src || ''} alt={alt} />
      <AvatarFallback className={cn(
        "bg-amber-200 text-amber-800",
        fallbackSizeClasses[size]
      )}>
        {fallback}
      </AvatarFallback>
    </Avatar>
  );
}
