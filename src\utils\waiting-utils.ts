"use client";

import { createClient } from "../../supabase/client";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { getMatchCurrentState } from "./database";

export interface WaitingUser {
  id: string;
  name: string;
  avatar?: string;
  initials: string;
}

export interface LeaderboardUser {
  id: string;
  name: string | null;
  avatar_url: string | null;
  score: number;
  rank: number;
}

const supabase = createClient();

export async function checkUserInAnyActiveMatch(userId: string) {
  try {
    const supabase = createClient();
    const { data: playerMatches, error: playerMatchesError } = await supabase
      .from('match_players')
      .select('match_id')
      .eq('player_id', userId);

    if (playerMatchesError) {
      console.error('Error checking if user is in any match:', playerMatchesError);
      return null;
    }

    if (!playerMatches || playerMatches.length === 0) {
      return null;
    }

    const matchIds = playerMatches.map(pm => pm.match_id);
    const { data: activeMatches, error: matchesError } = await supabase
      .from('matches')
      .select('id, difficulty, status')
      .in('id', matchIds)
      .in('status', ['waiting', 'ongoing']);

    if (matchesError) {
      console.error('Error checking active matches:', matchesError);
      return null;
    }

    if (!activeMatches || activeMatches.length === 0) {
      return null;
    }

    return {
      id: activeMatches[0].id,
      difficulty: activeMatches[0].difficulty,
      status: activeMatches[0].status
    };
  } catch (error) {
    console.error('Exception checking if user is in any active match:', error);
    return null;
  }
}

export async function checkAndRegisterUser(
  supabase: ReturnType<typeof createClient>,
  roomName: string,
  router: ReturnType<typeof useRouter>,
  setIsCheckingRegistration: (value: boolean) => void,
  setRegistrationError: (value: string | null) => void,
  setIsRegistered: (value: boolean) => void,
  setClientGameState: (state: any) => void
) {
  try {
    setIsCheckingRegistration(true);
    setRegistrationError(null);
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      console.error("Error getting user:", userError);
      setRegistrationError("User not authenticated");
      setIsCheckingRegistration(false);
      return false;
    }

    const activeMatch = await checkUserInAnyActiveMatch(user.id);

    if (activeMatch) {
      const currentDifficulty = roomName.toLowerCase();
      const userMatchDifficulty = activeMatch.difficulty.toLowerCase();

      if (userMatchDifficulty !== currentDifficulty) {
        setRegistrationError(`You are already in the ${activeMatch.difficulty} room. Please leave that room first before joining the ${roomName} room.`);
        setIsCheckingRegistration(false);
        return false;
      }

      if (userMatchDifficulty === currentDifficulty && activeMatch.status === 'ongoing') {
        const matchState = await getMatchCurrentState(activeMatch.id);
        console.log("User is already in an ongoing match:", matchState);
        setClientGameState(matchState)
        setIsCheckingRegistration(false);
        return true;
      }
    }

    const difficulty = roomName.toLowerCase();
    const { data: existingWaitingMatches, error: matchCheckError } = await supabase
      .from('matches')
      .select(`id, status, match_players!inner(player_id)`)
      .eq('status', 'waiting')
      .eq('difficulty', difficulty)
      .eq('match_players.player_id', user.id);

    if (matchCheckError) {
      console.error("Error checking existing matches:", matchCheckError);
    } else if (existingWaitingMatches && existingWaitingMatches.length > 0) {
      setIsRegistered(true);
      setIsCheckingRegistration(false);
      return true;
    }

    setRegistrationError(`You are not registered in this room. Please join from the dashboard first.`);
    console.log(`You are not registered in this room. Please join from the dashboard first.`)
    setIsCheckingRegistration(false);
    return false;
  } catch (error) {
    console.error("Error in checkAndRegisterUser:", error);
    setRegistrationError("An unexpected error occurred");
    return false;
  }
}

async function attemptSetStartTime(matchId: string, playerCount: number, initialTimeInSeconds: number) {
  if (playerCount < 2) return false;

  try {
    const supabase = createClient();
    const { data: currentMatch, error: fetchError } = await supabase
      .from('matches')
      .select('start_time, status')
      .eq('id', matchId)
      .single();

    if (fetchError || !currentMatch) {
      console.error("Error fetching current match:", fetchError);
      return false;
    }

    if (currentMatch.start_time || currentMatch.status !== 'waiting') {
      return false;
    }

    const startTime = new Date();
    startTime.setSeconds(startTime.getSeconds() + initialTimeInSeconds);
    const startTimeISO = startTime.toISOString();

    const { data: updateResult, error: updateError } = await supabase
      .from('matches')
      .update({ start_time: startTimeISO })
      .eq('id', matchId)
      .is('start_time', null)
      .select();

    if (updateError) {
      console.error("Error setting start time:", updateError);
      return false;
    }

    return !!(updateResult && updateResult.length > 0);
  } catch (error) {
    console.error("Exception setting start time:", error);
    return false;
  }
}

async function attemptResetStartTime(matchId: string, playerCount: number) {
  if (playerCount >= 2) return false;

  try {
    const supabase = createClient();
    const { data: resetResult, error: resetError } = await supabase
      .from('matches')
      .update({ start_time: null })
      .eq('id', matchId)
      .eq('status', 'waiting')
      .select();

    if (resetError) {
      console.error("Error resetting start time:", resetError);
      return false;
    }

    return !!(resetResult && resetResult.length > 0);
  } catch (error) {
    console.error("Exception resetting start time:", error);
    return false;
  }
}

export async function fetchWaitingUsers(
  supabase: ReturnType<typeof createClient>,
  roomName: string,
  setWaitingUsers: (users: WaitingUser[]) => void,
  setPlayerCount: (count: number) => void,
  setCountdownActive: (active: boolean) => void,
  setCurrentMatchId: (id: string | null) => void,
  setTimeLeft: (time: number) => void,
  initialTimeInSeconds: number
) {
  try {
    const difficulty = roomName.toLowerCase();
    const { data: { user } } = await supabase.auth.getUser();
    const currentUserId = user?.id;

    const { data, error } = await supabase
      .from('matches')
      .select(`id, status, start_time, match_players(player_id, players(id, display_name, avatar_url))`)
      .eq('status', 'waiting')
      .eq('difficulty', difficulty);

    if (error) {
      console.error("Error fetching waiting users:", error);
      return;
    }

    if (!data || data.length === 0) {
      setWaitingUsers([]);
      setPlayerCount(0);
      setCountdownActive(false);
      setCurrentMatchId(null);
      return;
    }

    const players: WaitingUser[] = [];
    const playerIds = new Set();
    let matchId: string | null = null;
    let currentMatchStatus = 'waiting';
    let currentStartTime: string | null = null;

    data.forEach((match: any) => {
      if (!matchId) {
        matchId = match.id;
        currentMatchStatus = match.status;
        currentStartTime = match.start_time;
      }

      match.match_players.forEach((mp: any) => {
        if (mp.players && !playerIds.has(mp.players.id)) {
          playerIds.add(mp.players.id);

          const name = mp.players.display_name || 'Anonymous Player';
          const initials = name.split(' ').map((n: string) => n[0]).join('').toUpperCase().substring(0, 2);

          players.push({
            id: mp.players.id,
            name: mp.players.id === currentUserId ? `${name} (You)` : name,
            avatar: mp.players.avatar_url,
            initials
          });
        }
      });
    });

    setWaitingUsers(players);
    setPlayerCount(players.length);
    setCurrentMatchId(matchId);

    if (matchId && currentMatchStatus === 'waiting') {
      if (players.length >= 2 && !currentStartTime) {
        const success = await attemptSetStartTime(matchId, players.length, initialTimeInSeconds);
        if (success) {
          setCountdownActive(true);
        }
      } else if (players.length < 2 && currentStartTime) {
        const success = await attemptResetStartTime(matchId, players.length);
        if (success) {
          setCountdownActive(false);
          setTimeLeft(initialTimeInSeconds);
        }
      } else if (currentStartTime) {
        setCountdownActive(true);
      } else {
        setCountdownActive(false);
      }
    }
  } catch (error) {
    console.error("Exception fetching waiting users:", error);
  }
}

export async function performHeartbeatSync(
  supabase: ReturnType<typeof createClient>,
  currentMatchId: string | null,
  countdownActive: boolean,
  setCountdownActive: (active: boolean) => void,
  setTimeLeft: (time: number) => void,
  initialTimeInSeconds: number,
  router: ReturnType<typeof useRouter>,
  roomName: string
) {
  try {
    if (!currentMatchId) return;

    const { data: currentMatch, error } = await supabase
      .from('matches')
      .select('id, status, start_time')
      .eq('id', currentMatchId)
      .single();

    if (error || !currentMatch) {
      console.error("Heartbeat sync error:", error);
      return;
    }

    const now = new Date();
    const startTime = currentMatch.start_time ? new Date(currentMatch.start_time) : null;

    if (currentMatch.status === 'waiting') {
      if (startTime) {
        if (!countdownActive) {
          setCountdownActive(true);
          const newTimeLeft = Math.max(0, Math.floor((startTime.getTime() - now.getTime()) / 1000));
          setTimeLeft(newTimeLeft);
        }
      } else {
        if (countdownActive) {
          setCountdownActive(false);
          setTimeLeft(initialTimeInSeconds);
        }
      }
    } else if (currentMatch.status === 'ongoing') {
      router.replace(`/battle/${roomName.toLowerCase()}`);
    }
  } catch (error) {
    console.error("Exception in heartbeat sync:", error);
  }
}

export function calculateTimeLeft(startTime: string) {
  const startTimeDate = new Date(startTime);
  const currentTime = new Date();
  const diffMs = startTimeDate.getTime() - currentTime.getTime();
  const seconds = diffMs / 1000;
  return Math.max(0, parseFloat(seconds.toFixed(2)));
}

export function formatTime(seconds: number) {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins}:${secs < 10 ? "0" : ""}${secs.toFixed(0)}`;
}

export async function leaveRoom(
  roomName: string, 
  onLeaveRoom: (() => void) | undefined,
  router: ReturnType<typeof useRouter>,
  setIsLeaving: (leaving: boolean) => void
) {
  try {
    setIsLeaving(true);
    const supabase = createClient();
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      console.error("Error getting user:", userError);
      toast.error("Error leaving room: User not authenticated");
      setIsLeaving(false);
      return;
    }

    const difficulty = roomName.toLowerCase();
    const { data: activeMatches, error: matchesError } = await supabase
      .from('matches')
      .select('id, status')
      .in('status', ['waiting', 'ongoing'])
      .eq('difficulty', difficulty);

    if (matchesError) {
      console.error("Error finding active matches:", matchesError);
      toast.error("Error leaving room: Could not find active matches");
      setIsLeaving(false);
      return;
    }

    if (!activeMatches || activeMatches.length === 0) {
      toast.info("No active room to leave");
      setIsLeaving(false);
      if (onLeaveRoom) onLeaveRoom();
      else router.replace('/dashboard');
      return;
    }

    let deletionSuccessful = false;
    for (const match of activeMatches) {
      const { data: playerInMatch, error: checkError } = await supabase
        .from('match_players')
        .select('*')
        .eq('match_id', match.id)
        .eq('player_id', user.id);

      if (checkError) continue;
      if (!playerInMatch || playerInMatch.length === 0) continue;

      const { error: deleteError } = await supabase
        .from('match_players')
        .delete()
        .eq('match_id', match.id)
        .eq('player_id', user.id)
        .select();

      if (!deleteError) {
        deletionSuccessful = true;
      }
    }

    if (!deletionSuccessful) {
      toast.error("Failed to leave the room. Please try again.");
      setIsLeaving(false);
      window.location.href = '/dashboard';
      return;
    }

    toast.success("Successfully left the room");
    if (onLeaveRoom) onLeaveRoom();
    else router.replace('/dashboard');
  } catch (error) {
    console.error("Error leaving room:", error);
    toast.error("An unexpected error occurred while leaving the room");
  } finally {
    setIsLeaving(false);
  }
}

export async function resetStartTime(matchId: string) {
  try {
    const { data: match, error: startTimeError } = await supabase
      .from('matches')
      .select('start_time')
      .eq('id', matchId)
      .single();
    
    if (startTimeError) {
      console.error("Error fetching start time:", startTimeError);
      return false;
    }

    if (match && match.start_time) {
      const { error: resetError } = await supabase
        .from('matches')
        .update({ start_time: null })
        .eq('id', matchId);

      if (resetError) {
        console.error("Error resetting start time:", resetError);
        return false;
      }

      return true;
    }

  } catch (error) {
    console.error("Error resetting start time:", error);
  }

  return false;
}