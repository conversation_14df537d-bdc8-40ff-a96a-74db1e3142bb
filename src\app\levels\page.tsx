import React from 'react';
import { createClient } from '../../../supabase/server';
import { redirect } from 'next/navigation';
import DashboardNavbar from "@/components/dashboard-navbar";
import {
  Trophy,
  Star,
  Award,
  TrendingUp,
  Users,
  Clock,
  Medal,
  Sparkles,
  Zap,
  Crown,
  Check
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";
import { getLevelLeaderboard, getXPMilestones, formatXP, getLevelInfo, getLevelTitle } from "@/utils/level-system";
import StyledAvatar from "@/components/styled-avatar";
import LevelDisplay from "@/components/level-display";

export default async function LevelsPage() {
  const supabase = await createClient();

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return redirect("/sign-in");
  }

  // Get player data for current user
  const { data: playerData } = await supabase
    .from('players')
    .select('*')
    .eq('id', user.id)
    .single();

  const currentXP = playerData?.level_experience || 0;
  const levelInfo = getLevelInfo(currentXP);

  // Get level leaderboard
  const leaderboard = await getLevelLeaderboard(20);
  
  // Get XP milestones
  const milestones = getXPMilestones();

  // Helper function to get rank styling
  const getRankStyling = (index: number) => {
    if (index === 0) return "bg-gradient-to-r from-yellow-400 to-yellow-500 text-white shadow-lg";
    if (index === 1) return "bg-gradient-to-r from-gray-300 to-gray-400 text-white shadow-md";
    if (index === 2) return "bg-gradient-to-r from-amber-600 to-amber-700 text-white shadow-md";
    return "bg-gradient-to-r from-amber-100 to-amber-200 text-amber-800";
  };

  return (
    <>
      <DashboardNavbar />
      <main className="w-full bg-gradient-to-br from-amber-50/50 via-white to-amber-50/30 min-h-screen">
        <div className="container mx-auto px-4 py-8 flex flex-col gap-8">
          {/* Header Section */}
          <header className="text-center space-y-4">
            <div className="flex items-center justify-center gap-3">
              <Star className="h-8 w-8 text-amber-500" />
              <h1 className="text-4xl font-bold bg-gradient-to-r from-amber-600 to-amber-800 bg-clip-text text-transparent">
                Level System
              </h1>
              <Star className="h-8 w-8 text-amber-500" />
            </div>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Earn XP by playing matches and climb the ranks! Higher ranks unlock exclusive avatars, backgrounds, and borders.
            </p>
          </header>

          {/* Your Level Card */}
          <section>
            <Card className="border-2 border-amber-200 shadow-lg overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-amber-100 to-amber-50 pb-6">
                <CardTitle className="text-xl text-amber-900 flex items-center gap-2">
                  <Star className="h-5 w-5 text-amber-600" />
                  Your Level
                </CardTitle>
                <CardDescription className="text-sm">
                  Your current level and progress
                </CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <LevelDisplay 
                  totalXP={currentXP}
                  variant="card"
                  showProgress={true}
                  showTitle={true}
                />
              </CardContent>
            </Card>
          </section>

          {/* Level Leaderboard */}
          <section className="space-y-4">
            <div className="flex items-center gap-2">
              <Trophy className="h-6 w-6 text-amber-600" />
              <h2 className="text-2xl font-bold text-amber-900">Level Leaderboard</h2>
            </div>
            
            <Card className="border shadow-sm">
              <CardContent className="p-0">
                <div className="divide-y">
                  {leaderboard.map((player, index) => (
                    <div
                      key={player.id}
                      className={cn(
                        "flex items-center justify-between p-4 hover:bg-gray-50/50 transition-colors",
                        index === 0 ? "bg-amber-50/50" : ""
                      )}
                    >
                      <div className="flex items-center gap-4">
                        <div className={cn(
                          "w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold",
                          getRankStyling(index)
                        )}>
                          {index + 1}
                        </div>
                        <StyledAvatar
                          src={player.avatar_url}
                          alt={player.display_name}
                          fallback={player.display_name.substring(0, 2).toUpperCase()}
                          size="sm"
                          border={player.avatar_border}
                        />
                        <div className="flex-1 min-w-0">
                          <p className="font-semibold text-gray-900 truncate">{player.display_name}</p>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge className={cn(
                              "text-xs",
                              player.levelInfo.currentLevel >= 50 ? "bg-yellow-100 text-yellow-800" :
                              player.levelInfo.currentLevel >= 25 ? "bg-purple-100 text-purple-800" :
                              player.levelInfo.currentLevel >= 15 ? "bg-blue-100 text-blue-800" :
                              player.levelInfo.currentLevel >= 5 ? "bg-green-100 text-green-800" :
                              "bg-gray-100 text-gray-800"
                            )}>
                              {getLevelTitle(player.levelInfo.currentLevel)}
                            </Badge>
                          </div>
                        </div>
                      </div>
                      
                      <div className="text-right">
                        <p className="font-bold text-lg text-amber-700">
                          Level {player.levelInfo.currentLevel}
                        </p>
                        <p className="text-sm text-gray-600">
                          {formatXP(player.level_experience)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </section>

          {/* Level Milestones */}
          <section className="space-y-4">
            <div className="flex items-center gap-2">
              <Award className="h-6 w-6 text-amber-600" />
              <h2 className="text-2xl font-bold text-amber-900">Level Milestones</h2>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {milestones.filter(m => m.rewards.length > 0).map((milestone) => (
                <Card 
                  key={milestone.level}
                  className={cn(
                    "border overflow-hidden transition-all duration-200 hover:shadow-md",
                    levelInfo.currentLevel >= milestone.level ? "bg-gradient-to-r from-green-50 to-white border-green-200" : 
                    "bg-white border-gray-200 opacity-80"
                  )}
                >
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-center">
                      <CardTitle className="text-lg flex items-center gap-2">
                        <Star className={cn(
                          "h-5 w-5",
                          levelInfo.currentLevel >= milestone.level ? "text-green-500" : "text-gray-400"
                        )} />
                        Level {milestone.level}
                      </CardTitle>
                      <Badge className={cn(
                        levelInfo.currentLevel >= milestone.level ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"
                      )}>
                        {levelInfo.currentLevel >= milestone.level ? "Unlocked" : formatXP(milestone.xp - currentXP) + " to go"}
                      </Badge>
                    </div>
                    <CardDescription>
                      {formatXP(milestone.xp)} total XP required
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <p className="text-sm font-medium text-gray-700">Rewards:</p>
                      <div className="flex flex-wrap gap-2">
                        {milestone.rewards.map((reward, i) => (
                          <Badge 
                            key={i}
                            variant="outline" 
                            className={cn(
                              "text-xs",
                              levelInfo.currentLevel >= milestone.level ? "border-green-300 text-green-700" : "border-gray-300 text-gray-500"
                            )}
                          >
                            {levelInfo.currentLevel >= milestone.level && <Check className="h-3 w-3 mr-1" />}
                            {reward}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>
        </div>
      </main>
    </>
  );
}
